#!/usr/bin/env python3
"""
Скрипт для сбора статистики торговых ботов из целевого чата
Собирает информацию о сработавших стоп-лоссах и тейк-профитах
"""

import asyncio
import json
import os
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

from telethon import TelegramClient
from telethon.tl.types import Message
from dotenv import load_dotenv


@dataclass
class TradeStats:
    """Статистика по торговым операциям"""
    channel: str
    stop_losses: int = 0
    take_profits_tp1: int = 0
    total_trades: int = 0
    
    def to_dict(self):
        return asdict(self)


@dataclass
class CollectorState:
    """Состояние коллектора для продолжения работы"""
    min_message_id: int = 0  # Минимальный ID обработанного сообщения
    max_message_id: int = 0  # Максимальный ID обработанного сообщения
    last_message_date: str = ""
    total_messages_processed: int = 0
    
    def to_dict(self):
        return asdict(self)


class TradingStatsCollector:
    """Коллектор статистики торговых ботов"""
    
    def __init__(self, config_path: str = ".env", reset_stats: bool = False):
        self.config_path = config_path
        self.stats_file = "result.json"
        self.state_file = "collector_state.json"
        self.reset_stats = reset_stats
        
        # Загружаем конфигурацию
        load_dotenv(config_path)
        
        self.api_id = int(os.getenv("API_ID", "35898"))
        self.api_hash = os.getenv("API_HASH", "ee2681a1110b14bffb5ba15f8f1ce3ee")
        self.phone = os.getenv("PHONE", "+79169380360")
        self.session_name = os.getenv("SESSION_NAME", "stats")
        self.target_chat_id = int(os.getenv("TARGET_CHAT_ID", "-4984770976"))
        
        # Инициализируем структуры данных
        self.stats: Dict[str, TradeStats] = {}
        self.state = CollectorState()
        
        # Кэш для связи ID сделок с каналами
        self.trade_channel_cache: Dict[str, str] = {}
        
        # Регулярные выражения для парсинга сообщений
        self.patterns = {
            'take_profit_tp1': re.compile(r'✅ Тейк профит 1 сделки ([a-f0-9]+) исполнен @ \$([\d.]+)'),
            'stop_loss': re.compile(r'🛡️ Стоп лосс сделки ([a-f0-9]+) исполнен @ \$([\d.]+)'),
            'new_trade': re.compile(r'\*\*Канал:\*\* (.+?)(?:\n|\*\*)'),
            'trade_id': re.compile(r'\*\*ID сделки:\*\* `([a-f0-9]+)`')
        }
    
    async def start(self):
        """Запуск коллектора"""
        print(f"🚀 Запуск коллектора статистики торговых ботов")
        print(f"📊 Целевой чат: {self.target_chat_id}")
        
        # Создаем клиент Telegram
        client = TelegramClient(self.session_name, self.api_id, self.api_hash)
        
        try:
            await client.start(phone=self.phone)
            print("✅ Подключение к Telegram успешно")
            
            # Загружаем сохраненное состояние
            if self.reset_stats:
                print("🔄 Режим сброса: очистка статистики и состояния")
                self.state = CollectorState()
                self.stats = {}
            else:
                await self.load_state()
                await self.load_stats()
            
            # Собираем статистику
            await self.collect_stats(client)
            
            # Сохраняем результаты
            await self.save_stats()
            await self.save_state()
            
            # Выводим итоговую статистику
            await self.print_summary()
            
        except Exception as e:
            print(f"❌ Ошибка: {e}")
        finally:
            await client.disconnect()
            print("🔌 Отключение от Telegram")
    
    async def load_state(self):
        """Загрузка состояния коллектора"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)
                    # Поддержка старого формата с last_message_id
                    if 'last_message_id' in state_data and 'min_message_id' not in state_data:
                        old_id = state_data['last_message_id']
                        state_data['min_message_id'] = old_id
                        state_data['max_message_id'] = old_id
                        del state_data['last_message_id']
                    
                    self.state = CollectorState(**state_data)
                    print(f"📥 Загружено состояние: диапазон {self.state.min_message_id} - {self.state.max_message_id}")
            else:
                print("🆕 Создано новое состояние коллектора")
        except Exception as e:
            print(f"⚠️ Ошибка загрузки состояния: {e}")
    
    async def save_state(self):
        """Сохранение состояния коллектора"""
        try:
            if os.path.dirname(self.state_file):
                os.makedirs(os.path.dirname(self.state_file), exist_ok=True)
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(self.state.to_dict(), f, ensure_ascii=False, indent=2)
            print(f"💾 Состояние сохранено: диапазон {self.state.min_message_id} - {self.state.max_message_id}")
        except Exception as e:
            print(f"⚠️ Ошибка сохранения состояния: {e}")
    
    async def load_stats(self):
        """Загрузка существующей статистики"""
        try:
            if os.path.exists(self.stats_file):
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    stats_data = json.load(f)
                    for channel, data in stats_data.items():
                        self.stats[channel] = TradeStats(**data)
                    print(f"📊 Загружена статистика по {len(self.stats)} каналам")
            else:
                print("🆕 Создана новая статистика")
        except Exception as e:
            print(f"⚠️ Ошибка загрузки статистики: {e}")
    
    async def save_stats(self):
        """Сохранение статистики"""
        try:
            if os.path.dirname(self.stats_file):
                os.makedirs(os.path.dirname(self.stats_file), exist_ok=True)
            stats_dict = {channel: stats.to_dict() for channel, stats in self.stats.items()}
            
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats_dict, f, ensure_ascii=False, indent=2)
            
            print(f"💾 Статистика сохранена в {self.stats_file}")
        except Exception as e:
            print(f"⚠️ Ошибка сохранения статистики: {e}")
    
    async def collect_stats(self, client):
        """Сбор статистики из чата"""
        print(f"🔍 Начинаем сбор статистики...")
        
        try:
            # Получаем сообщения из целевого чата
            print(f"🔍 Получение сообщений из чата {self.target_chat_id}")
            print(f"📍 Диапазон обработанных: {self.state.min_message_id} - {self.state.max_message_id}")
            
            # Получаем сообщения в зависимости от режима
            all_messages = []
            
            if self.reset_stats or (self.state.max_message_id == 0 and self.state.min_message_id == 0):
                print("🔍 Получение ВСЕХ сообщений в чате...")
                
                # Получаем сообщения порциями для эффективности
                total_fetched = 0
                
                # Получаем ВСЕ сообщения в чате
                print("🔍 Загрузка всех сообщений...")
                
                # Метод 1: Получаем последние сообщения
                print("🔍 Метод 1: Получение последних сообщений...")
                batch = await client.get_messages(
                    self.target_chat_id,
                    limit=1000
                )
                
                if batch:
                    all_messages.extend(batch)
                    total_fetched += len(batch)
                    print(f"📨 Получено {total_fetched} сообщений... (ID: {batch[0].id} - {batch[-1].id})")
                    
                    # Метод 2: Получаем более старые сообщения через offset_id
                    print("🔍 Метод 2: Получение более старых сообщений...")
                    offset_id = batch[-1].id
                    iteration = 1
                    
                    while offset_id and len(batch) == 1000:
                        try:
                            print(f"🔍 Итерация {iteration}: получение сообщений старше {offset_id}...")
                            batch = await client.get_messages(
                                self.target_chat_id,
                                limit=1000,
                                offset_id=offset_id
                            )
                            
                            if batch and len(batch) > 0:
                                all_messages.extend(batch)
                                total_fetched += len(batch)
                                print(f"📨 Получено {total_fetched} сообщений... (ID: {batch[0].id} - {batch[-1].id})")
                                
                                if batch[-1].id:
                                    offset_id = batch[-1].id
                                    iteration += 1
                                else:
                                    print("⚠️ Последнее сообщение не имеет ID")
                                    break
                            else:
                                print("📭 Больше сообщений не найдено")
                                break
                                
                        except Exception as e:
                            print(f"⚠️ Ошибка при получении старых сообщений: {e}")
                            break
                    
                    print(f"🔍 Метод 2 завершен после {iteration} итераций")
                    
                    # Метод 3: Попробуем получить самые старые сообщения через min_id
                    if all_messages:
                        min_id = min(msg.id for msg in all_messages if msg.id)
                        print(f"🔍 Метод 3: Попытка получить сообщения старше {min_id}...")
                        
                        try:
                            older_batch = await client.get_messages(
                                self.target_chat_id,
                                limit=1000,
                                min_id=min_id
                            )
                            
                            if older_batch:
                                print(f"📨 Найдено {len(older_batch)} более старых сообщений")
                                all_messages.extend(older_batch)
                                print(f"✅ Итого: {len(all_messages)} сообщений")
                            else:
                                print("📭 Более старых сообщений не найдено")
                        except Exception as e:
                            print(f"⚠️ Не удалось получить более старые сообщения: {e}")
                    
                    # Метод 4: Попробуем получить сообщения через add_offset
                    print("🔍 Метод 4: Попытка получить сообщения через add_offset...")
                    try:
                        add_offset_batch = await client.get_messages(
                            self.target_chat_id,
                            limit=1000,
                            add_offset=len(all_messages)
                        )
                        
                        if add_offset_batch:
                            print(f"📨 Найдено {len(add_offset_batch)} сообщений через add_offset")
                            all_messages.extend(add_offset_batch)
                            print(f"✅ Итого: {len(all_messages)} сообщений")
                        else:
                            print("📭 Сообщений через add_offset не найдено")
                    except Exception as e:
                        print(f"⚠️ Не удалось получить сообщения через add_offset: {e}")
                    
                    # Метод 5: Попробуем получить сообщения через max_id
                    if all_messages:
                        max_id = max(msg.id for msg in all_messages if msg.id)
                        print(f"🔍 Метод 5: Попытка получить сообщения через max_id...")
                        try:
                            max_id_batch = await client.get_messages(
                                self.target_chat_id,
                                limit=1000,
                                max_id=max_id
                            )
                            
                            if max_id_batch:
                                print(f"📨 Найдено {len(max_id_batch)} сообщений через max_id")
                                all_messages.extend(max_id_batch)
                                print(f"✅ Итого: {len(all_messages)} сообщений")
                            else:
                                print("📭 Сообщений через max_id не найдено")
                        except Exception as e:
                            print(f"⚠️ Не удалось получить сообщения через max_id: {e}")
                    
                    # Метод 6: Попробуем получить сообщения через комбинацию параметров
                    print("🔍 Метод 6: Попытка получить сообщения через комбинацию параметров...")
                    try:
                        # Получаем сообщения с большим лимитом
                        large_batch = await client.get_messages(
                            self.target_chat_id,
                            limit=5000
                        )
                        
                        if large_batch and len(large_batch) > len(all_messages):
                            new_messages = [msg for msg in large_batch if msg.id not in [m.id for m in all_messages if m.id]]
                            if new_messages:
                                print(f"📨 Найдено {len(new_messages)} новых сообщений через большой лимит")
                                all_messages.extend(new_messages)
                                print(f"✅ Итого: {len(all_messages)} сообщений")
                            else:
                                print("📭 Новых сообщений через большой лимит не найдено")
                        else:
                            print("📭 Сообщений через большой лимит не найдено")
                    except Exception as e:
                        print(f"⚠️ Не удалось получить сообщения через большой лимит: {e}")
                
                print(f"✅ Всего получено {len(all_messages)} сообщений")
                
                # Проверяем, есть ли дубликаты
                unique_ids = set()
                for msg in all_messages:
                    if msg.id:
                        unique_ids.add(msg.id)
                
                print(f"🔍 Уникальных сообщений: {len(unique_ids)}")
                if len(unique_ids) != len(all_messages):
                    print(f"⚠️ Обнаружены дубликаты: {len(all_messages) - len(unique_ids)}")
                
                # Метод 3: Попробуем получить самые старые сообщения через min_id
                if all_messages:
                    min_id = min(msg.id for msg in all_messages if msg.id)
                    print(f"🔍 Метод 3: Попытка получить сообщения старше {min_id}...")
                    
                    try:
                        older_batch = await client.get_messages(
                            self.target_chat_id,
                            limit=1000,
                            min_id=min_id
                        )
                        
                        if older_batch:
                            print(f"📨 Найдено {len(older_batch)} более старых сообщений")
                            all_messages.extend(older_batch)
                            print(f"✅ Итого: {len(all_messages)} сообщений")
                        else:
                            print("📭 Более старых сообщений не найдено")
                    except Exception as e:
                        print(f"⚠️ Не удалось получить более старые сообщения: {e}")
                
            else:
                print("🔍 Получение новых и старых сообщений...")
                
                # Шаг 1: Получаем новые сообщения выше max_message_id
                new_messages = []
                if self.state.max_message_id > 0:
                    print(f"🔍 Шаг 1: Получение новых сообщений выше {self.state.max_message_id}")
                    new_messages = await client.get_messages(
                        self.target_chat_id,
                        limit=1000,
                        offset_id=self.state.max_message_id
                    )
                    if new_messages:
                        print(f"📨 Найдено {len(new_messages)} новых сообщений")
                    else:
                        print("📭 Новых сообщений не найдено")
                
                # Шаг 2: Получаем старые сообщения ниже min_message_id
                old_messages = []
                if self.state.min_message_id > 0:
                    print(f"🔍 Шаг 2: Получение старых сообщений ниже {self.state.min_message_id}")
                    old_messages = await client.get_messages(
                        self.target_chat_id,
                        limit=1000,
                        offset_id=self.state.min_message_id
                    )
                    if old_messages:
                        print(f"📨 Найдено {len(old_messages)} старых сообщений")
                    else:
                        print("📭 Старых сообщений не найдено")
                
                all_messages = new_messages + old_messages
            
            if not all_messages:
                print("📭 Сообщений для обработки не найдено")
                return
            
            print(f"📨 Всего найдено {len(all_messages)} сообщений для обработки")
            
            # Проверим первые несколько сообщений на валидность
            print("🔍 Проверка первых сообщений...")
            for i, msg in enumerate(all_messages[:3]):
                text_preview = msg.text[:100] if msg.text else "Нет текста"
                print(f"   Сообщение {i}: ID={msg.id}, текст: {text_preview}...")
            
            # Сначала проходим по всем сообщениям для построения кэша каналов
            print("🔍 Построение кэша каналов для сделок...")
            
            # Давайте найдем сообщения с новыми сделками для отладки
            new_trade_messages = []
            for msg in all_messages:
                if msg.text and "НОВАЯ СДЕЛКА" in msg.text:
                    new_trade_messages.append(msg)
            print(f"🔍 Найдено {len(new_trade_messages)} сообщений с 'НОВАЯ СДЕЛКА'")
            
            if new_trade_messages:
                print("📝 Пример сообщения о новой сделке:")
                print(f"   {new_trade_messages[0].text[:300]}...")
            
            await self.build_channel_cache(all_messages)
            
            # Обрабатываем сообщения с обновлением диапазона
            processed = 0
            for message in all_messages:
                if message.id is None:
                    print(f"⚠️ Пропускаем сообщение с ID=None")
                    continue
                
                # Проверяем, не обрабатывали ли мы уже это сообщение
                if (self.state.min_message_id <= message.id <= self.state.max_message_id and 
                    self.state.min_message_id > 0 and self.state.max_message_id > 0):
                    continue
                
                await self.process_message(message)
                self.state.total_messages_processed += 1
                processed += 1
                
                # Обновляем диапазон обработанных сообщений
                if self.state.min_message_id == 0:
                    self.state.min_message_id = message.id
                    self.state.max_message_id = message.id
                else:
                    self.state.min_message_id = min(self.state.min_message_id, message.id)
                    self.state.max_message_id = max(self.state.max_message_id, message.id)
                
                # Обновляем дату последнего сообщения
                if message.date:
                    self.state.last_message_date = message.date.isoformat()
                
                # Показываем прогресс каждые 100 сообщений
                if processed % 100 == 0:
                    print(f"📊 Обработано {processed} сообщений")
            
            print(f"✅ Обработка завершена. Всего обработано: {processed}")
            print(f"📍 Новый диапазон: {self.state.min_message_id} - {self.state.max_message_id}")
            
        except Exception as e:
            import traceback
            print(f"❌ Ошибка при сборе статистики: {e}")
            print("🔍 Подробности ошибки:")
            traceback.print_exc()
    
    async def build_channel_cache(self, messages: List[Message]):
        """Построение кэша каналов для сделок"""
        cache_built = 0
        
        for message in messages:
            if not message or not message.text:
                continue
            
            text = message.text
            
            # Ищем сообщения о новых сделках
            new_trade_match = self.patterns['new_trade'].search(text)
            if new_trade_match:
                channel = new_trade_match.group(1).strip()
                trade_id_match = self.patterns['trade_id'].search(text)
                if trade_id_match:
                    trade_id = trade_id_match.group(1)
                    self.trade_channel_cache[trade_id] = channel
                    cache_built += 1
        
        print(f"📋 Построен кэш для {cache_built} сделок")
    
    async def process_message(self, message: Message):
        """Обработка одного сообщения"""
        if not message or not message.text:
            return
        
        text = message.text
        
        # Проверяем на тейк-профит 1
        tp1_match = self.patterns['take_profit_tp1'].search(text)
        if tp1_match:
            trade_id = tp1_match.group(1)
            price = float(tp1_match.group(2))
            await self.handle_take_profit_tp1(trade_id, price, message)
            return
        
        # Проверяем на стоп-лосс
        sl_match = self.patterns['stop_loss'].search(text)
        if sl_match:
            trade_id = sl_match.group(1)
            price = float(sl_match.group(2))
            await self.handle_stop_loss(trade_id, price, message)
            return
        
        # Проверяем на новую сделку
        new_trade_match = self.patterns['new_trade'].search(text)
        if new_trade_match:
            channel = new_trade_match.group(1).strip()
            trade_id_match = self.patterns['trade_id'].search(text)
            if trade_id_match:
                trade_id = trade_id_match.group(1)
                await self.handle_new_trade(channel, trade_id, message)
    
    async def handle_take_profit_tp1(self, trade_id: str, price: float, message: Message):
        """Обработка первого тейк-профита"""
        channel = self.trade_channel_cache.get(trade_id, "Неизвестный канал")
        
        if channel not in self.stats:
            self.stats[channel] = TradeStats(channel=channel)
        
        self.stats[channel].take_profits_tp1 += 1
        print(f"💰 TP1: {channel} - {trade_id} @ ${price}")
    
    async def handle_stop_loss(self, trade_id: str, price: float, message: Message):
        """Обработка стоп-лосса"""
        channel = self.trade_channel_cache.get(trade_id, "Неизвестный канал")
        
        if channel not in self.stats:
            self.stats[channel] = TradeStats(channel=channel)
        
        self.stats[channel].stop_losses += 1
        print(f"🛡️ SL: {channel} - {trade_id} @ ${price}")
    
    async def handle_new_trade(self, channel: str, trade_id: str, message: Message):
        """Обработка новой сделки"""
        if channel not in self.stats:
            self.stats[channel] = TradeStats(channel=channel)
        
        self.stats[channel].total_trades += 1
        print(f"📊 Новая сделка: {channel} - {trade_id}")
    
    async def print_summary(self):
        """Вывод итоговой статистики"""
        print("\n" + "="*60)
        print("📊 ИТОГОВАЯ СТАТИСТИКА ТОРГОВЫХ БОТОВ")
        print("="*60)
        
        if not self.stats:
            print("📭 Статистика не найдена")
            return
        
        # Сортируем каналы по общему количеству сделок
        sorted_channels = sorted(
            self.stats.values(), 
            key=lambda x: x.total_trades, 
            reverse=True
        )
        
        total_sl = sum(stats.stop_losses for stats in self.stats.values())
        total_tp1 = sum(stats.take_profits_tp1 for stats in self.stats.values())
        total_trades = sum(stats.total_trades for stats in self.stats.values())
        
        print(f"📈 ОБЩАЯ СТАТИСТИКА:")
        print(f"   Всего сделок: {total_trades}")
        print(f"   Стоп-лоссов: {total_sl}")
        print(f"   Тейк-профитов TP1: {total_tp1}")
        print(f"   Соотношение SL/TP1: {total_sl/total_tp1:.2f}" if total_tp1 > 0 else "   Соотношение SL/TP1: N/A")
        
        print(f"\n📊 ПО КАНАЛАМ:")
        for stats in sorted_channels:
            if stats.total_trades > 0:
                sl_ratio = stats.stop_losses / stats.total_trades * 100 if stats.total_trades > 0 else 0
                tp1_ratio = stats.take_profits_tp1 / stats.total_trades * 100 if stats.total_trades > 0 else 0
                
                print(f"   {stats.channel}:")
                print(f"     Сделок: {stats.total_trades}")
                print(f"     SL: {stats.stop_losses} ({sl_ratio:.1f}%)")
                print(f"     TP1: {stats.take_profits_tp1} ({tp1_ratio:.1f}%)")
                print()
        
        print(f"💾 Результаты сохранены в: {self.stats_file}")
        print(f"🔄 Состояние сохранено в: {self.state_file}")


async def main():
    """Главная функция"""
    import sys
    
    # Проверяем аргументы командной строки
    reset_stats = False
    if len(sys.argv) > 1 and sys.argv[1] in ['--reset', '-r', '--clear', '-c']:
        reset_stats = True
        print("🔄 Режим сброса статистики активирован")
    
    collector = TradingStatsCollector(reset_stats=reset_stats)
    await collector.start()


if __name__ == "__main__":
    asyncio.run(main())
