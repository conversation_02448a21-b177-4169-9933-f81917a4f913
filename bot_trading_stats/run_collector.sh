#!/bin/bash

# Скрипт для запуска сборщика статистики торговых ботов

echo "🚀 Запуск сборщика статистики торговых ботов..."
echo "📁 Текущая директория: $(pwd)"

# Проверяем наличие .env файла
if [ ! -f ".env" ]; then
    echo "⚠️  Файл .env не найден. Копирую из env.example..."
    cp env.example .env
    echo "📝 Отредактируйте .env файл перед запуском!"
    echo "   Не забудьте указать правильные API_ID, API_HASH и PHONE"
    exit 1
fi

# Проверяем наличие Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 не найден. Установите Python 3.8+"
    exit 1
fi

# Проверяем зависимости
echo "🔍 Проверка зависимостей..."
if ! python3 -c "import telethon" &> /dev/null; then
    echo "📦 Устанавливаю зависимости..."
    pip3 install -r requirements.txt
fi

# Проверяем аргументы
RESET_MODE=""
if [ "$1" = "--reset" ] || [ "$1" = "-r" ] || [ "$1" = "--clear" ] || [ "$1" = "-c" ]; then
    RESET_MODE="--reset"
    echo "🔄 Режим сброса статистики активирован"
fi

# Запускаем коллектор
echo "▶️  Запуск коллектора..."
python3 trading_stats_collector.py $RESET_MODE

echo "✅ Сбор статистики завершен!"
echo "📊 Для просмотра результатов запустите: python3 view_stats.py"
