# 🚀 Быстрый запуск сборщика статистики

## 1. Перейдите в папку
```bash
cd bot_trading_stats
```

## 2. Запустите автоматическую настройку
```bash
# Обычный запуск
./run_collector.sh

# Сброс статистики и парсинг с нуля
./run_collector.sh --reset
```

## 3. Следуйте инструкциям на экране
- Введите номер телефона
- Введите код подтверждения из Telegram
- Дождитесь завершения

## 4. Просмотр результатов
```bash
python3 view_stats.py
```

## 📁 Файлы
- `trading_stats_collector.py` - основной скрипт
- `view_stats.py` - просмотр статистики  
- `run_collector.sh` - автоматический запуск
- `README.md` - полная документация

## ⚠️ Важно
Перед первым запуском отредактируйте `.env` файл:
- Укажите правильный `PHONE`
- Проверьте `API_ID` и `API_HASH`
