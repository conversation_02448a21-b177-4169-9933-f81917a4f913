#!/usr/bin/env python3
"""
Упрощенный коллектор статистики торговых ботов
Использует прямую итерацию по ID сообщений
"""

import asyncio
import os
import json
import re
from datetime import datetime
from dataclasses import dataclass, asdict
from dotenv import load_dotenv
from telethon import TelegramClient

@dataclass
class TradeStats:
    """Статистика по каналу"""
    channel: str
    stop_losses: int = 0
    take_profits_tp1: int = 0
    total_trades: int = 0

@dataclass
class CollectorState:
    """Состояние коллектора для продолжения работы"""
    min_message_id: int = 0  # Минимальный ID обработанного сообщения
    max_message_id: int = 0  # Максимальный ID обработанного сообщения
    last_message_date: str = ""
    total_messages_processed: int = 0

class SimpleTradingStatsCollector:
    """Упрощенный коллектор статистики торговых ботов"""
    
    def __init__(self, config_path: str = ".env", reset_stats: bool = False):
        self.config_path = config_path
        self.stats_file = "./simple_result.json"
        self.state_file = "./simple_collector_state.json"
        self.reset_stats = reset_stats
        
        # Загружаем конфигурацию
        load_dotenv(config_path)
        self.api_id = int(os.getenv('API_ID', '35898'))
        self.api_hash = os.getenv('API_HASH', 'ee2681a1110b14bffb5ba15f8f1ce3ee')
        self.phone = os.getenv('PHONE', '+79169380360')
        self.session_name = os.getenv('SESSION_NAME', 'stats')
        self.target_chat_id = int(os.getenv('TARGET_CHAT_ID', '-4984770976'))
        
        # Статистика
        self.stats = {}
        self.state = CollectorState()
        
        # Кэш каналов для сделок
        self.trade_channel_cache = {}
        
        # Паттерны для парсинга
        self.patterns = {
            'take_profit_tp1': re.compile(r'✅ Тейк профит 1 сделки ([a-f0-9]+) исполнен @ \$([\d.]+)'),
            'stop_loss': re.compile(r'🛡️ Стоп лосс сделки ([a-f0-9]+) исполнен @ \$([\d.]+)'),
            'new_trade': re.compile(r'\*\*Канал:\*\* (.+?)(?:\n|\*\*)'),
            'trade_id': re.compile(r'\*\*ID сделки:\*\* `([a-f0-9]+)`')
        }
    
    async def start(self):
        """Запуск коллектора"""
        print("🚀 Запуск упрощенного коллектора статистики торговых ботов")
        print(f"📊 Целевой чат: {self.target_chat_id}")
        
        try:
            # Подключаемся к Telegram
            self.client = TelegramClient(self.session_name, self.api_id, self.api_hash)
            await self.client.start(phone=self.phone)
            print("✅ Подключение к Telegram успешно")
            
            # Загружаем сохраненное состояние
            if self.reset_stats:
                print("🔄 Режим сброса: очистка статистики и состояния")
                self.state = CollectorState()
                self.stats = {}
            else:
                await self.load_state()
                await self.load_stats()
            
            print(f"📍 Диапазон обработанных: {self.state.min_message_id} - {self.state.max_message_id}")
            
            # Собираем статистику
            await self.collect_stats_simple()
            
            # Сохраняем результаты
            await self.save_stats()
            await self.save_state()
            
            # Выводим итоги
            self.print_summary()
            
        except Exception as e:
            print(f"❌ Ошибка: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            if hasattr(self, 'client'):
                await self.client.disconnect()
                print("🔌 Отключение от Telegram")
    
    async def collect_stats_simple(self):
        """Собирает статистику, проходя ВСЕ сообщения в чате"""
        print("🔄 Начинаем сбор статистики...")
        
        # Получаем информацию о чате
        try:
            chat = await self.client.get_entity(self.target_chat_id)
            print(f"📱 Анализируем чат: {chat.title}")
        except Exception as e:
            print(f"❌ Ошибка получения информации о чате: {e}")
            return

        # Определяем диапазон ID сообщений
        print("🔍 Определяем диапазон ID сообщений...")
        try:
            # Получаем последние сообщения для определения максимального ID
            recent_messages = await self.client.get_messages(self.target_chat_id, limit=10)
            if not recent_messages:
                print("📭 Сообщения не найдены")
                return
            
            max_id = max(msg.id for msg in recent_messages if msg.id)
            print(f"📊 Максимальный ID сообщения: {max_id}")
            
            # Начинаем с ID 1 (или с минимального доступного)
            start_id = 1
            print(f"🚀 Начинаем парсинг с ID сообщения: {start_id}")
            
        except Exception as e:
            print(f"❌ Ошибка при определении диапазона: {e}")
            return

        # Используем тот же подход, что и в основном проекте - iter_messages
        print(f"📦 Получаем все сообщения используя iter_messages...")
        messages_processed = 0
        
        try:
            # Собираем все сообщения сначала
            print("📦 Собираем все сообщения...")
            all_messages = []
            async for message in self.client.iter_messages(self.target_chat_id, min_id=start_id):
                if message and message.id:
                    all_messages.append(message)
            
            print(f"📨 Получено {len(all_messages)} сообщений")
            
            # Сортируем сообщения от старых к новым (как в основном проекте)
            print("🔄 Сортируем сообщения от старых к новым для правильного построения кэша...")
            all_messages.sort(key=lambda x: x.id)
            
            # Обрабатываем сообщения в правильном порядке
            for message in all_messages:
                # Обрабатываем сообщение
                await self.process_message(message)
                messages_processed += 1
                
                # Обновляем состояние
                if self.state.min_message_id == 0 or message.id < self.state.min_message_id:
                    self.state.min_message_id = message.id
                if message.id > self.state.max_message_id:
                    self.state.max_message_id = message.id
                
                # Показываем прогресс каждые 100 сообщений
                if messages_processed % 100 == 0:
                    print(f"📊 Обработано сообщений: {messages_processed}, текущий ID: {message.id}")
                    await self.save_state()
                
                # Показываем прогресс каждые 500 сообщений
                if messages_processed % 500 == 0:
                    await self.save_state()
                    print(f"💾 Промежуточное сохранение состояния...")
            
            print(f"✅ Обработка завершена")
            
        except Exception as e:
            print(f"❌ Ошибка при получении сообщений: {e}")
            import traceback
            traceback.print_exc()
            return
        
        self.state.total_messages_processed = messages_processed
        print(f"🎯 Всего обработано сообщений: {messages_processed}")
        print(f"📈 Финальный диапазон ID: {self.state.min_message_id} - {self.state.max_message_id}")
        
        # Проверяем, сколько сообщений мы получили
        if messages_processed > 0:
            print(f"✅ Успешно обработаны все доступные сообщения в диапазоне {start_id} - {max_id}")
        else:
            print(f"⚠️ Не удалось получить сообщения в диапазоне {start_id} - {max_id}")
    
    async def process_message(self, message):
        """Обработка одного сообщения"""
        if not message or not message.text:
            return
            
        text = message.text
        
        # Проверяем на тейк-профит TP1
        tp1_match = self.patterns['take_profit_tp1'].search(text)
        if tp1_match:
            trade_id = tp1_match.group(1)
            price = tp1_match.group(2)
            channel = self.trade_channel_cache.get(trade_id, "Неизвестный канал")
            
            await self.handle_take_profit_tp1(channel, trade_id, price)
            return
        
        # Проверяем на стоп-лосс
        sl_match = self.patterns['stop_loss'].search(text)
        if sl_match:
            trade_id = sl_match.group(1)
            price = sl_match.group(2)
            channel = self.trade_channel_cache.get(trade_id, "Неизвестный канал")
            
            await self.handle_stop_loss(channel, trade_id, price)
            return
        
        # Проверяем на новую сделку
        if "НОВАЯ СДЕЛКА" in text:
            channel_match = self.patterns['new_trade'].search(text)
            trade_id_match = self.patterns['trade_id'].search(text)
            
            if channel_match and trade_id_match:
                channel = channel_match.group(1).strip()
                trade_id = trade_id_match.group(1).strip()
                
                await self.handle_new_trade(channel, trade_id)
    
    async def handle_take_profit_tp1(self, channel: str, trade_id: str, price: str):
        """Обработка тейк-профита TP1"""
        if channel not in self.stats:
            self.stats[channel] = TradeStats(channel=channel)
        
        self.stats[channel].take_profits_tp1 += 1
        print(f"💰 TP1: {channel} - {trade_id} @ ${price}")
    
    async def handle_stop_loss(self, channel: str, trade_id: str, price: str):
        """Обработка стоп-лосса"""
        if channel not in self.stats:
            self.stats[channel] = TradeStats(channel=channel)
        
        self.stats[channel].stop_losses += 1
        print(f"🛡️ SL: {channel} - {trade_id} @ ${price}")
    
    async def handle_new_trade(self, channel: str, trade_id: str):
        """Обработка новой сделки"""
        if channel not in self.stats:
            self.stats[channel] = TradeStats(channel=channel)
        
        self.stats[channel].total_trades += 1
        # Сохраняем в кэш для связи с SL/TP1
        self.trade_channel_cache[trade_id] = channel
        print(f"📊 Новая сделка: {channel} - {trade_id}")
    
    async def load_state(self):
        """Загрузка состояния"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)
                    # Поддержка старого формата с last_message_id
                    if 'last_message_id' in state_data and 'min_message_id' not in state_data:
                        old_id = state_data['last_message_id']
                        state_data['min_message_id'] = old_id
                        state_data['max_message_id'] = old_id
                    self.state = CollectorState(**state_data)
                    print(f"📥 Загружено состояние: диапазон {self.state.min_message_id} - {self.state.max_message_id}")
            else:
                print("🆕 Создано новое состояние коллектора")
        except Exception as e:
            print(f"⚠️ Ошибка при загрузке состояния: {e}")
            self.state = CollectorState()
    
    async def save_state(self):
        """Сохранение состояния"""
        try:
            os.makedirs(os.path.dirname(self.state_file), exist_ok=True)
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(self.state), f, ensure_ascii=False, indent=2)
            print(f"💾 Состояние сохранено: диапазон {self.state.min_message_id} - {self.state.max_message_id}")
        except Exception as e:
            print(f"❌ Ошибка при сохранении состояния: {e}")
    
    async def load_stats(self):
        """Загрузка статистики"""
        try:
            if os.path.exists(self.stats_file):
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    stats_data = json.load(f)
                    self.stats = {k: TradeStats(**v) for k, v in stats_data.items()}
                    print(f"📥 Загружена статистика: {len(self.stats)} каналов")
            else:
                print("🆕 Создана новая статистика")
        except Exception as e:
            print(f"❌ Ошибка при загрузке статистики: {e}")
            self.stats = {}
    
    async def save_stats(self):
        """Сохранение статистики"""
        try:
            os.makedirs(os.path.dirname(self.stats_file), exist_ok=True)
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                stats_dict = {k: asdict(v) for k, v in self.stats.items()}
                json.dump(stats_dict, f, ensure_ascii=False, indent=2)
            print(f"💾 Статистика сохранена в {self.stats_file}")
        except Exception as e:
            print(f"❌ Ошибка при сохранении статистики: {e}")
    
    def print_summary(self):
        """Вывод итоговой статистики"""
        print("\n" + "=" * 60)
        print("📊 ИТОГОВАЯ СТАТИСТИКА ТОРГОВЫХ БОТОВ")
        print("=" * 60)
        
        if not self.stats:
            print("📭 Статистика не найдена")
            return
        
        # Общая статистика
        total_trades = sum(s.total_trades for s in self.stats.values())
        total_sl = sum(s.stop_losses for s in self.stats.values())
        total_tp1 = sum(s.take_profits_tp1 for s in self.stats.values())
        
        print(f"📈 ОБЩАЯ СТАТИСТИКА:")
        print(f"   Всего сделок: {total_trades}")
        print(f"   Стоп-лоссов: {total_sl}")
        print(f"   Тейк-профитов TP1: {total_tp1}")
        if total_tp1 > 0:
            print(f"   Соотношение SL/TP1: {total_sl/total_tp1:.2f}")
        
        print(f"\n📊 ПО КАНАЛАМ:")
        
        # Сортируем по количеству сделок
        sorted_stats = sorted(self.stats.values(), key=lambda x: x.total_trades, reverse=True)
        
        for stat in sorted_stats:
            if stat.total_trades > 0:
                sl_percent = (stat.stop_losses / stat.total_trades * 100) if stat.total_trades > 0 else 0
                tp1_percent = (stat.take_profits_tp1 / stat.total_trades * 100) if stat.total_trades > 0 else 0
                
                print(f"   {stat.channel}:")
                print(f"     Сделок: {stat.total_trades}")
                print(f"     SL: {stat.stop_losses} ({sl_percent:.1f}%)")
                print(f"     TP1: {stat.take_profits_tp1} ({tp1_percent:.1f}%)")
        
        print(f"\n💾 Результаты сохранены в: {self.stats_file}")
        print(f"🔄 Состояние сохранено в: {self.state_file}")

async def main():
    """Главная функция"""
    import sys
    
    # Проверяем аргументы командной строки
    reset_stats = False
    if len(sys.argv) > 1 and sys.argv[1] in ['--reset', '-r', '--clear', '-c']:
        reset_stats = True
        print("🔄 Режим сброса статистики активирован")
    
    collector = SimpleTradingStatsCollector(reset_stats=reset_stats)
    await collector.start()

if __name__ == "__main__":
    asyncio.run(main())
