# Сборщик статистики торговых ботов

Скрипт для автоматического сбора статистики по торговым ботам из целевого Telegram чата.

## Возможности

- 📊 Сбор статистики по стоп-лоссам и тейк-профитам
- 🔍 Автоматическое определение каналов для сделок
- 💾 Сохранение прогресса (не нужно парсить все заново)
- 📈 Детальная статистика по каждому каналу
- 🔄 Возможность повторного запуска с продолжением

## Быстрый старт

1. **Перейдите в папку проекта:**
```bash
cd bot_trading_stats
```

2. **Запустите автоматическую настройку:**
```bash
./run_collector.sh
```

3. **Для сброса статистики и парсинга с нуля:**
```bash
./run_collector.sh --reset
```

3. **При первом запуске:**
   - Введите номер телефона
   - Введите код подтверждения из Telegram
   - Дождитесь завершения сбора статистики

4. **Просмотр результатов:**
```bash
python3 view_stats.py
```

## Ручная установка

### 1. Установка зависимостей
```bash
pip install -r requirements.txt
```

### 2. Настройка конфигурации
```bash
cp env.example .env
```

Отредактируйте `.env` файл:
```env
DOLPHIN_TELEGRAM_BOT_TOKEN=your_bot_token_here
TARGET_CHAT_ID=-4984770976
API_ID=35898
API_HASH=ee2681a1110b14bffb5ba15f8f1ce3ee
PHONE=+79169380360
SESSION_NAME=stats
OUTPUT_FILE=stats.txt
```

**Важно:** Укажите правильные `API_ID`, `API_HASH` и `PHONE` для вашего аккаунта.

### 3. Запуск
```bash
# Обычный запуск (продолжение с сохраненного состояния)
python3 trading_stats_collector.py

# Сброс статистики и парсинг с нуля
python3 trading_stats_collector.py --reset
# или
python3 trading_stats_collector.py -r
```

## Что собирается

Скрипт анализирует сообщения и собирает:

- **Новые сделки**: ID, канал, сигнал, цена входа
- **Стоп-лоссы**: ID сделки, цена исполнения
- **Тейк-профиты TP1**: ID сделки, цена исполнения

## Результаты

Статистика сохраняется в файл `result.json` в формате:
```json
{
  "Нестеров в рынке": {
    "channel": "Нестеров в рынке",
    "stop_losses": 5,
    "take_profits_tp1": 12,
    "total_trades": 25
  }
}
```

## Состояние

Прогресс сбора сохраняется в `collector_state.json`:
- ID последнего обработанного сообщения
- Дата последнего сообщения
- Общее количество обработанных сообщений

## Повторный запуск

При повторном запуске скрипт автоматически продолжит с того места, где остановился, не обрабатывая уже проанализированные сообщения.

## Примеры сообщений

Скрипт распознает следующие типы сообщений:

```
🤖 ТОРГОВЫЙ БОТ: ✅ Тейк профит 1 сделки 3ef5ff94 исполнен @ $22.817
🤖 ТОРГОВЫЙ БОТ: 🛡️ Стоп лосс сделки f3bb1837 исполнен @ $4.3932
🤖 ТОРГОВЫЙ БОТ: 📊 НОВАЯ СДЕЛКА ОТКРЫТА
ID сделки: b4498257
Канал: Нестеров в рынке
```

## Структура файлов

```
bot_trading_stats/
├── trading_stats_collector.py  # Основной скрипт сбора
├── view_stats.py               # Просмотр статистики
├── run_collector.sh            # Автоматический запуск
├── requirements.txt            # Зависимости Python
├── env.example                 # Пример конфигурации
├── README.md                   # Документация
├── result.json                 # Результаты (создается автоматически)
└── collector_state.json        # Состояние (создается автоматически)
```

## Устранение проблем

### Ошибка авторизации
- Убедитесь, что указан правильный номер телефона
- Проверьте API_ID и API_HASH
- Попробуйте удалить файл сессии и авторизоваться заново

### Нет новых сообщений
- Проверьте TARGET_CHAT_ID в .env
- Убедитесь, что у бота есть доступ к чату

### Ошибки парсинга
- Проверьте формат сообщений в чате
- Убедитесь, что сообщения соответствуют ожидаемому формату
