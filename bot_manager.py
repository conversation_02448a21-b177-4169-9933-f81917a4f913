import os
import json
import asyncio
from datetime import datetime
from typing import Dict, Optional, List, Callable, Any
from dataclasses import dataclass, asdict
from signal_analyzer import TradingSignal
from trading_bot import BingXTradingBot, TradeInstance, OpenMode

@dataclass
class BotConfig:
    """Конфигурация торгового бота для источника"""
    source_channel: str
    max_volume_usd: float
    leverage: int = 10
    enabled: bool = True
    bot_function: Optional[str] = None  # Название функции бота
    portfolio_percent: Optional[float] = None  # Процент от портфеля (приоритет над max_volume_usd)
    max_portfolio_usage: float = 100  # Максимальный процент портфеля для торговли (остальное - резерв)
    open_mode: OpenMode = OpenMode.DEFAULT  # Режим открытия позиции
    move_stop_to_breakeven: bool = True  # Перемещать ли стоп-лосс на безубыток после первого TP
    max_take_profit_percent: float = 0.0  # Максимальная прибыль в процентах (0 = без ограничений)
    position_lifetime: str = "0s"  # Время жизни позиции в формате "1h30m", по умолчанию отключено
    created_at: Optional[str] = None
    last_signal_at: Optional[str] = None
    total_trades: int = 0
    successful_trades: int = 0
    total_pnl: float = 0.0

class TradingBotManager:
    """Менеджер торговых ботов"""
    
    def __init__(self, api_key: str, api_secret: str, testnet: bool = True, logger=None):
        self.api_key = api_key
        self.api_secret = api_secret
        self.testnet = testnet
        self.logger = logger
        
        self.bots: Dict[str, BingXTradingBot] = {}
        self.bot_configs: Dict[str, BotConfig] = {}
        self.active_trades: Dict[str, TradeInstance] = {}
        
        # Единый экземпляр CCXT для всех ботов (одна биржа, один аккаунт)
        self._shared_exchange = None
        self._exchange_initialized = False
        
        # Файл для сохранения состояния ботов
        self.bots_file = "bots.md"
        
        # Доступные функции ботов
        self.bot_functions = {
            "default": self._default_bot_function,
            "grid_open": self._grid_open_bot_function
        }
    
    async def initialize(self):
        """Инициализация менеджера"""
        try:
            # Инициализируем общий exchange
            await self._initialize_shared_exchange()
            
            # Загружаем конфигурацию ботов
            await self._load_bot_configs()
            
            if self.logger:
                await self.logger.success(f"Менеджер торговых ботов инициализирован. Загружено {len(self.bot_configs)} конфигураций")
            
            return True
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка инициализации менеджера ботов: {str(e)}", e)
            return False
    
    async def _initialize_shared_exchange(self):
        """Инициализирует единый экземпляр CCXT для всех ботов"""
        try:
            import ccxt
            
            exchange_config = {
                'apiKey': self.api_key,
                'secret': self.api_secret,
                'sandbox': self.testnet,
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'future',
                }
            }
            
            # Создаем единый exchange объект
            self._shared_exchange = ccxt.bingx(exchange_config)
            
            # В тестовой среде пропускаем реальное подключение к бирже
            if self.testnet:
                if self.logger:
                    await self.logger.success("Единый CCXT exchange инициализирован в тестовом режиме")
                self._exchange_initialized = True
                return True
            
            # Проверяем подключение к бирже только в продакшене
            markets = self._shared_exchange.load_markets()
            balance = self._shared_exchange.fetch_balance()
            
            if self.logger:
                await self.logger.success(f"Единый CCXT exchange инициализирован. Баланс: {balance.get('USDT', {}).get('free', 0)} USDT, Рынков: {len(markets)}")
            
            self._exchange_initialized = True
            return True
            
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка инициализации единого exchange: {str(e)}", e)
            return False
    
    async def _load_bot_configs(self):
        """Загружает конфигурации ботов из файла"""
        try:
            if os.path.exists(self.bots_file):
                with open(self.bots_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # Парсим markdown файл для извлечения конфигураций
                # Пока что создаем пустой файл если его нет
                if not content.strip():
                    await self._save_bot_configs()
            else:
                await self._save_bot_configs()
                
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка загрузки конфигураций ботов: {str(e)}", e)
    
    async def _save_bot_configs(self):
        """Сохраняет конфигурации ботов в файл"""
        try:
            content = "# Торговые боты\n\n"
            content += f"**Обновлено:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            
            if not self.bot_configs:
                content += "Нет активных конфигураций ботов.\n"
            else:
                for source, config in self.bot_configs.items():
                    status = "✅ Активен" if config.enabled else "❌ Отключен"
                    content += f"## {source}\n\n"
                    content += f"- **Статус:** {status}\n"
                    if config.portfolio_percent is not None:
                        content += f"- **Процент портфеля:** {config.portfolio_percent}%\n"
                    content += f"- **Максимальный объем:** ${config.max_volume_usd}\n"
                    content += f"- **Плечо:** {config.leverage}x\n"
                    content += f"- **Макс. использование портфеля:** {config.max_portfolio_usage}%\n"
                    content += f"- **Функция бота:** {config.bot_function or 'не задана'}\n"
                    content += f"- **Режим открытия:** {config.open_mode.value}\n"
                    content += f"- **Всего сделок:** {config.total_trades}\n"
                    content += f"- **Успешных сделок:** {config.successful_trades}\n"
                    content += f"- **Общий PnL:** ${config.total_pnl:.2f}\n"
                    content += f"- **Создан:** {config.created_at or 'неизвестно'}\n"
                    content += f"- **Последний сигнал:** {config.last_signal_at or 'нет'}\n\n"
            
            # Добавляем информацию об активных сделках
            if self.active_trades:
                content += "## Активные сделки\n\n"
                for trade_id, trade in self.active_trades.items():
                    content += f"### {trade_id}\n\n"
                    content += f"- **Канал:** {trade.source_channel}\n"
                    content += f"- **Сигнал:** {trade.signal.direction} {trade.signal.ticker} @ {trade.signal.entry_price}\n"
                    content += f"- **Статус:** {trade.status}\n"
                    content += f"- **Объем:** ${trade.actual_volume_usd:.2f}\n"
                    content += f"- **Текущий PnL:** ${trade.current_pnl:.2f}\n"
                    content += f"- **Создана:** {trade.created_at}\n\n"
            
            with open(self.bots_file, 'w', encoding='utf-8') as f:
                f.write(content)
                
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка сохранения конфигураций ботов: {str(e)}", e)
    
    async def register_bot_for_source(self, source_channel: str, max_volume_usd: float, leverage: int = 10, bot_function: str = "default", portfolio_percent: Optional[float] = None, max_portfolio_usage: float = 100, open_mode: OpenMode = OpenMode.DEFAULT, move_stop_to_breakeven: bool = True, max_take_profit_percent: float = 0.0):
        """Регистрирует бота для источника сигналов"""
        try:
            if bot_function not in self.bot_functions:
                if self.logger:
                    await self.logger.error(f"Неизвестная функция бота: {bot_function}")
                return False
            
            config = BotConfig(
                source_channel=source_channel,
                max_volume_usd=max_volume_usd,
                leverage=leverage,
                bot_function=bot_function,
                portfolio_percent=portfolio_percent,
                max_portfolio_usage=max_portfolio_usage,
                open_mode=open_mode,
                move_stop_to_breakeven=move_stop_to_breakeven,
                max_take_profit_percent=max_take_profit_percent,
                enabled=True,
                created_at=datetime.now().isoformat()
            )
            
            self.bot_configs[source_channel] = config
            
            # Проверяем, что общий exchange инициализирован
            if not self._exchange_initialized:
                if self.logger:
                    await self.logger.error("Общий exchange не инициализирован")
                return False
            
            # Создаем экземпляр бота с общим exchange
            bot = BingXTradingBot(self.api_key, self.api_secret, leverage, self.testnet, self.logger)
            # Заменяем его exchange на общий
            bot.exchange = self._shared_exchange
            bot.exchange_config = None  # Больше не нужно
            
            # Помечаем как инициализированный (без повторной инициализации exchange)
            self.bots[source_channel] = bot
            
            # Сохраняем конфигурации
            await self._save_bot_configs()
            
            if self.logger:
                await self.logger.success(f"Торговый бот зарегистрирован для канала: {source_channel} (использует общий exchange)")
            return True
                
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка регистрации бота для {source_channel}: {str(e)}", e)
            return False
    
    async def process_signal(self, signal: TradingSignal, source_channel: str) -> bool:
        """Обрабатывает торговый сигнал"""
        try:
            # Проверяем, есть ли бот для этого источника
            if source_channel not in self.bot_configs:
                if self.logger:
                    await self.logger.info(f"Нет конфигурации бота для канала: {source_channel}")
                return False
            
            config = self.bot_configs[source_channel]
            
            # Проверяем, включен ли бот
            if not config.enabled:
                if self.logger:
                    await self.logger.info(f"Бот для канала {source_channel} отключен")
                return False
            
            # Проверяем, есть ли функция бота
            if not config.bot_function:
                if self.logger:
                    await self.logger.info(f"Функция бота не задана для канала: {source_channel}")
                return False
            
            # Проверяем, есть ли активный бот
            if source_channel not in self.bots:
                if self.logger:
                    await self.logger.warning(f"Активный бот не найден для канала: {source_channel}")
                return False
            
            bot = self.bots[source_channel]
            bot_function = self.bot_functions[config.bot_function]
            
            # Выполняем функцию бота
            result = await bot_function(bot, signal, source_channel, config)
            
            if result:
                # Обновляем статистику
                config.last_signal_at = datetime.now().isoformat()
                config.total_trades += 1
                await self._save_bot_configs()
            
            return result
            
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка обработки сигнала от {source_channel}: {str(e)}", e)
            return False
    
    async def _default_bot_function(self, bot: BingXTradingBot, signal: TradingSignal, source_channel: str, config: BotConfig) -> bool:
        """Стандартная функция торгового бота"""
        try:
            trade = await bot.execute_signal(signal, source_channel, config.max_volume_usd, config.portfolio_percent, config.max_portfolio_usage, config.open_mode, config.move_stop_to_breakeven, config.max_take_profit_percent, config.position_lifetime)
            
            if trade:
                self.active_trades[trade.id] = trade
                await self._save_bot_configs()
                return True
            
            return False
            
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка в default_bot_function: {str(e)}", e)
            return False

    async def _grid_open_bot_function(self, bot: BingXTradingBot, signal: TradingSignal, source_channel: str, config: BotConfig) -> bool:
        """Функция торгового бота с режимом grid_open"""
        try:
            trade = await bot.execute_signal(signal, source_channel, config.max_volume_usd, config.portfolio_percent, config.max_portfolio_usage, OpenMode.GRID_OPEN, config.move_stop_to_breakeven, config.max_take_profit_percent, config.position_lifetime)
            
            if trade:
                self.active_trades[trade.id] = trade
                await self._save_bot_configs()
                return True
            
            return False
            
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка в grid_open_bot_function: {str(e)}", e)
            return False
    

    
    async def start_monitoring(self):
        """Запускает мониторинг всех ботов"""
        try:
            tasks = []
            
            # Запускаем мониторинг для каждого бота
            for source_channel, bot in self.bots.items():
                task = asyncio.create_task(bot.monitor_trades())
                tasks.append(task)
            
            # Запускаем задачу периодического сохранения
            save_task = asyncio.create_task(self._periodic_save())
            tasks.append(save_task)
            
            if tasks:
                await asyncio.gather(*tasks)
                
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка запуска мониторинга ботов: {str(e)}", e)
    
    async def _periodic_save(self):
        """Периодически сохраняет состояние ботов"""
        while True:
            try:
                await asyncio.sleep(300)  # Сохраняем каждые 5 минут
                await self._save_bot_configs()
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Ошибка периодического сохранения: {str(e)}", e)
    
    async def get_bot_stats(self) -> Dict[str, Any]:
        """Возвращает статистику ботов"""
        try:
            total_bots = len(self.bot_configs)
            active_bots = sum(1 for config in self.bot_configs.values() if config.enabled)
            total_trades = sum(config.total_trades for config in self.bot_configs.values())
            total_pnl = sum(config.total_pnl for config in self.bot_configs.values())
            active_trades_count = len(self.active_trades)
            
            return {
                "total_bots": total_bots,
                "active_bots": active_bots,
                "total_trades": total_trades,
                "total_pnl": total_pnl,
                "active_trades": active_trades_count,
                "bot_configs": {source: asdict(config) for source, config in self.bot_configs.items()},
                "active_trades": {trade_id: asdict(trade) for trade_id, trade in self.active_trades.items()}
            }
            
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка получения статистики ботов: {str(e)}", e)
            return {}
    
    async def disable_bot(self, source_channel: str) -> bool:
        """Отключает бота для источника"""
        try:
            if source_channel in self.bot_configs:
                self.bot_configs[source_channel].enabled = False
                await self._save_bot_configs()
                
                if self.logger:
                    await self.logger.info(f"Бот для канала {source_channel} отключен")
                return True
            
            return False
            
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка отключения бота для {source_channel}: {str(e)}", e)
            return False
    
    async def enable_bot(self, source_channel: str) -> bool:
        """Включает бота для источника"""
        try:
            if source_channel in self.bot_configs:
                self.bot_configs[source_channel].enabled = True
                await self._save_bot_configs()
                
                if self.logger:
                    await self.logger.info(f"Бот для канала {source_channel} включен")
                return True
            
            return False
            
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка включения бота для {source_channel}: {str(e)}", e)
            return False 

    async def force_sync_positions(self) -> Dict[str, Any]:
        """Принудительная синхронизация позиций с биржей для всех ботов"""
        try:
            sync_results = {}
            
            for source_channel, bot in self.bots.items():
                try:
                    untracked_positions = await bot.sync_positions_with_exchange()
                    sync_results[source_channel] = {
                        'success': True,
                        'untracked_positions': untracked_positions,
                        'count': len(untracked_positions)
                    }
                except Exception as e:
                    sync_results[source_channel] = {
                        'success': False,
                        'error': str(e),
                        'untracked_positions': [],
                        'count': 0
                    }
            
            return sync_results
            
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка принудительной синхронизации позиций: {str(e)}", e)
            return {} 