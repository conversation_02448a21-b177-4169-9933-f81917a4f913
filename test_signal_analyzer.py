#!/usr/bin/env python3
"""
Тестовый скрипт для проверки анализатора торговых сигналов
"""

import asyncio
import os
from dotenv import load_dotenv
from signal_analyzer import SignalAnalyzer

load_dotenv()

# Тестовые сообщения
TEST_SIGNALS = [
    # Пример 1: Short UNI/USDT
    """🖼 Short📉 UNI/USDT

Плечо: 15x
Вход: 7.110 / по рынку
Тейки: 7.003 / 6.795 / 6.469
Стоп: 7.356

До 2% от депозита!

❗️Фиксируем частями ❗️
1-ый тейк - 50%, 
2-ой тейк - 50%, 
3-ий тейк - закрываем позицию. 
После достижения: первого тейка переносим стоп на точку входа""",

    # Пример 2: Short ENA/USDT
    """🖼 Short 📉 ENA/USDT

Плечо: 20x
Вход:  0.2657 / по рынку 
Тейки: 0.2630 / 0.2555 / 0.2389
Стоп: 0.2730

До 3% от депозита!

❗️Фиксируем частями ❗️
1-ый тейк - 50%, 
2-ой тейк - 50%, 
3-ий тейк - закрываем позицию. 
После достижения: первого тейка переносим стоп на точку входа""",

    # Пример 3: Short AAVE/USDT
    """🖼 Short📉 AAVE/USDT

Плечо: 15x
Вход: 260.12 / по рынку 
Тейки: 256.13 / 249.17 / 238.79
Стоп: 270.82

До 3% от депозита!

❗️Фиксируем частями ❗️
1-ый тейк - 50%, 
2-ой тейк - 50%, 
3-ий тейк - закрываем позицию. 
После достижения: первого тейка переносим стоп на точку входа""",

    # Пример 4: Не торговый сигнал
    """📈 Bitcoin продолжает рост! 

Вчера цена BTC достигла новых высот.
Аналитики предсказывают дальнейший рост.
Рынок находится в состоянии эйфории.""",

    # Пример 5: Long сигнал (для тестирования)
    """🚀 Long📈 BTC/USDT

Плечо: 10x
Вход: 95000 / по рынку
Тейки: 96500 / 98000 / 100000
Стоп: 93000

До 5% от депозита!""",

    # Пример 6: Минимальный сигнал без SL/TP (для тестирования новой функции)
    """🔥 LONG ETH/USDT
📈 Вход: 2500""",

    # Пример 7: Сигнал только с направлением и тикером
    """🚀 SHORT BTC/USDT
💰 Вход: 45000"""
]

class TestLogger:
    """Простой логгер для тестов"""
    
    async def info(self, message):
        print(f"INFO: {message}")
    
    async def success(self, message):
        print(f"SUCCESS: {message}")
    
    async def warning(self, message):
        print(f"WARNING: {message}")
    
    async def error(self, message, exception=None):
        print(f"ERROR: {message}")
        if exception:
            print(f"Exception: {exception}")

async def test_signal_analyzer():
    """Тестирует анализатор сигналов"""
    
    # Проверяем наличие API ключа
    api_key = os.getenv('OPENROUTER_API_KEY')
    if not api_key:
        print("❌ OPENROUTER_API_KEY не найден в .env файле")
        print("Добавьте OPENROUTER_API_KEY=your_key_here в .env файл")
        return
    
    print("🎯 Запуск тестирования анализатора торговых сигналов")
    print("=" * 60)
    
    try:
        # Создаем анализатор
        logger = TestLogger()
        analyzer = SignalAnalyzer(logger=logger)
        
        print(f"✅ Анализатор инициализирован с {len(analyzer.preferred_models)} моделями")
        print(f"🔑 API ключ: {api_key[:10]}...{api_key[-5:]}")
        print("\n" + "=" * 60)
        
        # Тестируем каждое сообщение в обоих режимах
        for i, test_message in enumerate(TEST_SIGNALS, 1):
            print(f"\n🧪 Тест {i}/{len(TEST_SIGNALS)}")
            print("-" * 40)
            print(f"Сообщение: {test_message[:80]}...")

            # Используем текущее время для тестов
            from datetime import datetime
            test_date = datetime.now()
            test_channel = "Test Channel"

            # Тестируем стандартный режим (SL/TP обязательны)
            print("\n🔒 СТАНДАРТНЫЙ РЕЖИМ (SL/TP обязательны):")
            try:
                signal_standard = await analyzer.extract_signal(
                    test_message,
                    test_date,
                    test_channel,
                    allow_without_sl_tp=False
                )

                if signal_standard:
                    print("✅ Торговый сигнал найден!")
                    print(f"   {signal_standard.direction} {signal_standard.ticker} @ {signal_standard.entry_price}")
                    print(f"   SL: {signal_standard.stop_loss}, TP: {signal_standard.take_profits}")
                else:
                    print("❌ Торговый сигнал не найден")

            except Exception as e:
                print(f"❌ Ошибка при анализе: {e}")

            # Тестируем новый режим (SL/TP опциональны)
            print("\n🔓 НОВЫЙ РЕЖИМ (SL/TP опциональны):")
            try:
                signal_flexible = await analyzer.extract_signal(
                    test_message,
                    test_date,
                    test_channel,
                    allow_without_sl_tp=True
                )

                if signal_flexible:
                    print("✅ Торговый сигнал найден!")
                    print("📊 Извлеченные данные:")
                    print(f"   Направление: {signal_flexible.direction}")
                    print(f"   Тикер: {signal_flexible.ticker}")
                    print(f"   Плечо: {signal_flexible.leverage}x")
                    print(f"   Вход: {signal_flexible.entry_price}")
                    print(f"   Тейки: {signal_flexible.take_profits}")
                    print(f"   Стоп: {signal_flexible.stop_loss}")
                    print(f"   Депозит: {signal_flexible.deposit_percentage}%")

                    print("\n📋 JSON:")
                    print(analyzer.format_signal_json(signal_flexible))

                else:
                    print("❌ Торговый сигнал не найден")

            except Exception as e:
                print(f"❌ Ошибка при анализе: {e}")

            print("-" * 40)
        
        print("\n" + "=" * 60)
        print("🎉 Тестирование завершено!")
        
    except Exception as e:
        print(f"❌ Критическая ошибка: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Запускаем тест
    asyncio.run(test_signal_analyzer()) 