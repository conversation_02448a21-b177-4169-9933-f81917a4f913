#!/usr/bin/env python3
"""
Тест для проверки исправления размера стоп-лосса при перемещении на безубыток.

Проблема: После исполнения первого тейк-профита размер позиции уменьшается,
но стоп-лосс пытается создаться с исходным размером, что вызывает ошибку:
"The order size must be less than the available amount of 5.86 USDT"

Исправление: Получаем актуальный размер позиции с биржи перед созданием нового стоп-лосса.
"""

import asyncio
import sys
import os
from unittest.mock import Mock, AsyncMock, patch
from dataclasses import dataclass
from datetime import datetime

# Добавляем путь к проекту
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from trading_bot import BingXTradingBot, TradeInstance, TradeOrder, OrderStatus, OrderType, TradeStatus
from signal_analyzer import TradingSignal, SignalDirection

class TestStopLossSizeFix:
    """Тестирует исправление размера стоп-лосса"""
    
    def __init__(self):
        self.bot = None
        self.setup_bot()
    
    def setup_bot(self):
        """Настраивает мок торгового бота"""
        self.bot = BingXTradingBot(
            api_key="test_key",
            api_secret="test_secret",
            testnet=True
        )
        
        # Мокаем exchange
        self.bot.exchange = Mock()
        self.bot.exchange.create_order = Mock()
        self.bot.exchange.cancel_order = Mock()
        self.bot.exchange.fetch_positions = Mock()
        
        # Мокаем логирование
        self.bot._log_to_file = AsyncMock()
        self.bot._log_to_chat = AsyncMock()
        
        # Мокаем получение tick_size
        self.bot._get_tick_size = AsyncMock(return_value=0.01)
    
    def create_test_trade(self) -> TradeInstance:
        """Создает тестовую сделку"""
        signal = TradingSignal(
            ticker="SUPER/USDT:USDT",
            direction=SignalDirection.LONG,
            entry_price=1.0,
            stop_loss=0.95,
            take_profits=[1.02, 1.04, 1.06]
        )
        
        entry_order = TradeOrder(
            id="test_entry",
            symbol="SUPER/USDT:USDT",
            side="buy",
            amount=10.0,  # Исходный размер позиции
            filled_amount=10.0,  # Полностью исполнен
            price=1.0,
            order_type=OrderType.MARKET,
            status=OrderStatus.CLOSED,
            created_at=datetime.now().isoformat()
        )
        
        stop_loss_order = TradeOrder(
            id="test_stop",
            symbol="SUPER/USDT:USDT",
            side="sell",
            amount=10.0,  # Исходный размер
            price=0.95,
            order_type=OrderType.STOP_MARKET,
            status=OrderStatus.OPEN,
            exchange_order_id="stop_123",
            created_at=datetime.now().isoformat()
        )
        
        # Создаем тейк-профиты
        tp_orders = []
        for i, tp_price in enumerate([1.02, 1.04, 1.06]):
            tp_order = TradeOrder(
                id=f"test_tp_{i+1}",
                symbol="SUPER/USDT:USDT",
                side="sell",
                amount=3.33,  # 1/3 от позиции
                filled_amount=3.33,
                price=tp_price,
                order_type=OrderType.TAKE_PROFIT_MARKET,
                status=OrderStatus.CLOSED if i == 0 else OrderStatus.OPEN,  # Первый TP исполнен
                exchange_order_id=f"tp_{i+1}_123",
                created_at=datetime.now().isoformat()
            )
            tp_orders.append(tp_order)
        
        trade = TradeInstance(
            id="test_trade_123",
            signal=signal,
            source_channel="Test Channel",
            entry_order=entry_order,
            stop_loss_order=stop_loss_order,
            take_profit_orders=tp_orders,
            status=TradeStatus.ACTIVE,
            max_volume_usd=100.0,
            actual_volume_usd=100.0,
            entry_price=1.0,
            breakeven_moved=False,  # Еще не перемещен
            move_stop_to_breakeven=True,
            created_at=datetime.now().isoformat(),
            log_file="./test_trade_123-log.md"
        )
        
        return trade
    
    async def test_move_stop_to_breakeven_with_reduced_position(self):
        """Тестирует перемещение стоп-лосса с уменьшенным размером позиции"""
        print("🧪 Тест: Перемещение стоп-лосса с уменьшенным размером позиции")
        
        trade = self.create_test_trade()
        
        # Мокаем _verify_position_still_open для возврата уменьшенного размера
        # После исполнения первого TP (3.33) остается: 10.0 - 3.33 = 6.67
        actual_position_size = 6.67
        self.bot._verify_position_still_open = AsyncMock(return_value=(True, actual_position_size))
        
        # Мокаем успешное создание ордера
        self.bot.exchange.create_order.return_value = {'id': 'new_stop_456'}
        
        # Выполняем перемещение стоп-лосса
        await self.bot._move_stop_to_breakeven(trade)
        
        # Проверяем, что стоп-лосс был перемещен
        assert trade.breakeven_moved == True, "Стоп-лосс должен быть помечен как перемещенный"
        
        # Проверяем, что размер стоп-лосса обновлен
        assert trade.stop_loss_order.amount == actual_position_size, f"Размер стоп-лосса должен быть {actual_position_size}, а не {trade.stop_loss_order.amount}"
        
        # Проверяем, что create_order был вызван с правильным размером
        self.bot.exchange.create_order.assert_called_once()
        call_args = self.bot.exchange.create_order.call_args
        used_size = call_args[0][3]  # 4-й аргумент - размер
        assert used_size == actual_position_size, f"create_order должен быть вызван с размером {actual_position_size}, а не {used_size}"
        
        print(f"✅ Исходный размер позиции: 10.0")
        print(f"✅ Размер после исполнения TP: {actual_position_size}")
        print(f"✅ Размер нового стоп-лосса: {trade.stop_loss_order.amount}")
        print(f"✅ Стоп-лосс успешно перемещен на безубыток")
        
    async def test_position_already_closed(self):
        """Тестирует случай, когда позиция уже закрыта"""
        print("\n🧪 Тест: Позиция уже закрыта на бирже")

        # Сбрасываем мок
        self.bot.exchange.create_order.reset_mock()

        trade = self.create_test_trade()

        # Мокаем _verify_position_still_open для возврата закрытой позиции
        self.bot._verify_position_still_open = AsyncMock(return_value=(False, 0.0))

        # Выполняем перемещение стоп-лосса
        await self.bot._move_stop_to_breakeven(trade)

        # Проверяем, что стоп-лосс НЕ был перемещен
        assert trade.breakeven_moved == False, "Стоп-лосс не должен быть перемещен для закрытой позиции"

        # Проверяем, что create_order НЕ был вызван
        self.bot.exchange.create_order.assert_not_called()

        print(f"✅ Позиция закрыта - перемещение стоп-лосса отменено")
        
    async def test_exchange_error_handling(self):
        """Тестирует обработку ошибок биржи"""
        print("\n🧪 Тест: Обработка ошибок биржи")

        # Сбрасываем мок и настраиваем ошибку
        self.bot.exchange.create_order.reset_mock()
        self.bot.exchange.create_order.side_effect = Exception("The order size must be less than the available amount")

        trade = self.create_test_trade()

        # Мокаем _verify_position_still_open для возврата уменьшенного размера
        actual_position_size = 6.67
        self.bot._verify_position_still_open = AsyncMock(return_value=(True, actual_position_size))

        # Выполняем перемещение стоп-лосса
        await self.bot._move_stop_to_breakeven(trade)

        # Проверяем, что стоп-лосс был перемещен локально
        assert trade.breakeven_moved == True, "Стоп-лосс должен быть помечен как перемещенный локально"
        assert trade.stop_loss_order.amount == actual_position_size, f"Размер стоп-лосса должен быть обновлен локально"

        print(f"✅ Ошибка биржи обработана - стоп-лосс обновлен локально")
        
    async def run_all_tests(self):
        """Запускает все тесты"""
        print("🚀 Запуск тестов исправления размера стоп-лосса\n")
        
        try:
            await self.test_move_stop_to_breakeven_with_reduced_position()
            await self.test_position_already_closed()
            await self.test_exchange_error_handling()
            
            print(f"\n✅ Все тесты пройдены успешно!")
            print(f"✅ Исправление размера стоп-лосса работает корректно")
            
        except Exception as e:
            print(f"\n❌ Тест провален: {str(e)}")
            raise

async def main():
    """Главная функция"""
    test_runner = TestStopLossSizeFix()
    await test_runner.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
