# Индивидуальные Target Каналы для Source Каналов

## 🎯 Описание

Новая функциональность позволяет настроить индивидуальный целевой канал для каждого source канала в `SOURCE_CHANNELS`, вместо использования одного общего `TARGET_CHAT_ID` из .env файла.

## ⚙️ Настройка

### 1. Основная конфигурация в SOURCE_CHANNELS

```python
SOURCE_CHANNELS = {
    "Новости": {
        "id": -1001234567890,
        "forward_type": "custom",
        "target_chat_id": -4000000001  # Индивидуальный target для новостей
    },
    "Торговые сигналы": {
        "id": -1002667675217,
        "forward_type": "custom",
        "signal_fn": "signal_analyzer",
        "signals_only": True,
        "target_chat_id": -4000000002  # Индивидуальный target для сигналов
    },
    "Общий канал": {
        "id": -1001987654321,
        "forward_type": "repost"
        # target_chat_id не указан - будет использован TARGET_CHAT_ID из .env
    }
}
```

### 2. Глобальный TARGET_CHAT_ID в .env

```env
# Используется как fallback для каналов без target_chat_id
TARGET_CHAT_ID = -4984770976
```

## 🔧 Логика приоритетов

Система определяет целевой канал в следующем порядке:

1. **target_chat_id канала** (если указан) - **высший приоритет**
2. **глобальный TARGET_CHAT_ID** (если target_chat_id не указан)
3. **режим мониторинга** (если ни один не указан)

## 📊 Возможные сценарии

### Сценарий 1: Полная индивидуализация
```python
SOURCE_CHANNELS = {
    "Crypto News": {
        "id": -1001111111111,
        "forward_type": "custom",
        "target_chat_id": -4000000001  # Канал для криптоновостей
    },
    "Trading Signals": {
        "id": -1002222222222,
        "forward_type": "custom",
        "signal_fn": "signal_analyzer",
        "target_chat_id": -4000000002  # Канал для торговых сигналов
    },
    "Market Analytics": {
        "id": -1003333333333,
        "forward_type": "repost",
        "target_chat_id": -4000000003  # Канал для аналитики
    }
}
```

### Сценарий 2: Смешанная конфигурация
```python
SOURCE_CHANNELS = {
    "VIP Signals": {
        "id": -1001234567890,
        "forward_type": "custom",
        "signal_fn": "signal_analyzer",
        "target_chat_id": -4000000001  # VIP канал
    },
    "Public News": {
        "id": -1001987654321,
        "forward_type": "custom"
        # Без target_chat_id - используется глобальный TARGET_CHAT_ID
    },
    "Test Channel": {
        "id": -1001122334455,
        "forward_type": "custom"
        # Без target_chat_id - использует TARGET_CHAT_ID или мониторинг
    }
}
```

### Сценарий 3: Только мониторинг
```python
SOURCE_CHANNELS = {
    "Research Channel": {
        "id": -1001234567890,
        "forward_type": "custom"
        # Без target_chat_id + TARGET_CHAT_ID=None → только мониторинг
    }
}
```

## 🔍 Логирование

### При запуске
Система показывает подробную информацию о конфигурации каждого канала:

```
⚙️ Настройки каналов:
   • Crypto News: кастомная обработка → -4000000001 (индивидуальный)
   • Trading Signals: кастомная обработка + анализ сигналов → -4000000002 (индивидуальный)
   • Public News: кастомная обработка → -4984770976 (глобальный)
   • Test Channel: кастомная обработка → мониторинг

📊 Статистика каналов: 2 с индивидуальными target, 1 с глобальным, 1 в режиме мониторинга
```

### При обработке сообщений
```
✅ Переслано 3 сообщений из Crypto News в -4000000001
✅ Переслано 1 сообщений из Trading Signals в -4000000002
🔍 Обработано в режиме мониторинга: 2 сообщений из Test Channel
```

## ⚠️ Важные особенности

### 1. Обратная совместимость
- Существующие конфигурации продолжают работать без изменений
- Глобальный `TARGET_CHAT_ID` сохраняет своё значение как fallback

### 2. Валидация конфигурации
При запуске система проверяет:
- Наличие активных каналов
- Корректность target каналов
- Предупреждает о каналах в режиме мониторинга

### 3. Торговые боты
Торговые уведомления всё ещё используют глобальный `TARGET_CHAT_ID` через `CustomLogger.send_to_target_chat()` для единообразия.

## 🚀 Примеры использования

### Разделение по типам контента
```python
SOURCE_CHANNELS = {
    # Новости → новостной канал
    "CoinDesk": {
        "id": -1001111111111,
        "forward_type": "custom",
        "target_chat_id": -4000000001
    },
    
    # Торговые сигналы → торговый канал
    "Premium Signals": {
        "id": -1002222222222,
        "forward_type": "custom",
        "signal_fn": "signal_analyzer",
        "signals_only": True,
        "target_chat_id": -4000000002
    },
    
    # Аналитика → аналитический канал
    "Market Analysis": {
        "id": -1003333333333,
        "forward_type": "custom",
        "review": True,
        "target_chat_id": -4000000003
    }
}
```

### Тестирование и продакшн
```python
SOURCE_CHANNELS = {
    # Продакшн канал
    "Main Trading": {
        "id": -1001234567890,
        "forward_type": "custom",
        "signal_fn": "signal_analyzer",
        "target_chat_id": -4000000001  # Основной канал
    },
    
    # Тестовый канал
    "Test Signals": {
        "id": -1005555555555,
        "forward_type": "custom",
        "signal_fn": "signal_analyzer",
        "target_chat_id": -4000000002  # Тестовый канал
    }
}
```

## 📈 Преимущества

1. **Гибкость**: Каждый source канал может иметь свой target
2. **Организация**: Логическое разделение контента по назначению
3. **Масштабируемость**: Легко добавлять новые каналы с индивидуальными настройками
4. **Совместимость**: Полная обратная совместимость с существующими конфигурациями
5. **Мониторинг**: Возможность работать в режиме только мониторинга для отдельных каналов

## 🔧 Миграция с глобального TARGET_CHAT_ID

### Шаг 1: Оценка текущих каналов
Определите, какие каналы должны иметь индивидуальные target'ы.

### Шаг 2: Постепенная миграция
```python
# До (старая конфигурация)
SOURCE_CHANNELS = {
    "Channel1": {"id": -1001111111111, "forward_type": "custom"},
    "Channel2": {"id": -1002222222222, "forward_type": "repost"}
}

# После (новая конфигурация)
SOURCE_CHANNELS = {
    "Channel1": {
        "id": -1001111111111, 
        "forward_type": "custom",
        "target_chat_id": -4000000001  # Новый индивидуальный target
    },
    "Channel2": {
        "id": -1002222222222, 
        "forward_type": "repost"
        # Оставляем без target_chat_id - будет использоваться глобальный
    }
}
```

### Шаг 3: Тестирование
1. Добавьте `target_chat_id` к одному каналу
2. Проверьте логи на корректность пересылки
3. Постепенно мигрируйте остальные каналы

## 📝 Конфигурация .env

```env
# Глобальный fallback для каналов без индивидуального target_chat_id
TARGET_CHAT_ID = -4984770976

# Теперь каждый канал может иметь свой target в SOURCE_CHANNELS
# Приоритеты:
# 1. target_chat_id канала (если указан)
# 2. глобальный TARGET_CHAT_ID (если target_chat_id не указан)
# 3. режим мониторинга (если ни один не указан)
``` 