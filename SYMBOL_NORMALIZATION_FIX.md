# Исправление символов для BingX - ПОЛНЫЙ ОТЧЕТ

## Проблема
Торговый бот на BingX выдавал ошибки:
1. `bingx does not have market symbol QNTUSDT/HIFIUSDT/LINKUSDT/DOGEUSDT/SUPERUSDT`
2. `Permission denied as the API key was created without the permission, this api need Spot Trading permission`

**НОВЫЕ ПРОБЛЕМЫ** (после исправления символов):
3. `In the Hedge mode, the 'PositionSide' field can only be set to LONG or SHORT`
4. `amount of AAVE/USDT:USDT must be greater than minimum amount precision of 0.1`

**ФИНАЛЬНЫЕ ПРОБЛЕМЫ** (после исправления hedge mode):
5. `'>' not supported between instances of 'float' and 'NoneType'`
6. `Exchange.create_market_order() takes from 4 to 6 positional arguments but 7 were given`

**ФУНКЦИОНАЛЬНЫЕ ПРОБЛЕМЫ**:
7. Take Profit и Stop Loss ордера не размещались на бирже BingX

## Решение

### Этап 1: Исправление символов ✅
- Изменена логика `_normalize_symbol()` для использования ТОЛЬКО фьючерсных рынков
- Добавлена фильтрация по типу рынка: `swap`, `future`, `derivative`
- Исключены все spot-рынки чтобы избежать проблем с разрешениями API

### Этап 2: Исправление hedge mode и размера позиции ✅
- Добавлен параметр `positionSide` для BingX hedge mode
- Изменена функция `_calculate_position_size()` для учета минимальных размеров позиций
- Добавлены проверки лимитов биржи

### Этап 3: Финальные исправления ✅
- Добавлены None-safe проверки в функции расчета размера позиции
- Исправлен синтаксис вызова `create_order()` для CCXT
- Убраны лишние позиционные аргументы

### Этап 4: Реальное размещение TP/SL ордеров ✅
- Функции `_create_stop_loss()` и `_create_take_profits()` теперь размещают реальные ордера на BingX
- Добавлено детальное логирование процесса создания TP/SL
- Поддержка hedge mode для всех типов ордеров
- Резервное локальное сохранение при ошибках биржи
- Автоматическая корректировка размера TP ордеров (один TP вместо трех если размер мал)
- Увеличение размера позиции для поддержки нескольких TP ордеров
- Альтернативные типы ордеров при ошибках API

## Технические детали

### 1. Функция нормализации символов (только фьючерсы)
```python
async def _normalize_symbol(self, ticker: str) -> Optional[str]:
    markets = await self._get_markets()
    
    # Преобразование форматов
    if ticker.upper().endswith("USDT"):
        base = ticker[:-4].upper()
        possible_symbols.append(f"{base}/USDT:USDT")  # Только фьючерсы
    
    # Проверка типа рынка
    for symbol in possible_symbols:
        if symbol in markets:
            market_info = markets[symbol]
            market_type = market_info.get('type', '').lower()
            
            # ТОЛЬКО фьючерсы/swap
            if (market_info.get('active', True) and 
                market_type in ['swap', 'future', 'derivative']):
                return symbol
```

### 2. Расчет размера позиции с None-safe проверками
```python
async def _calculate_position_size(self, signal: TradingSignal, max_volume_usd: float, symbol: str) -> float:
    # Получаем лимиты рынка
    markets = await self._get_markets()
    market_info = markets.get(symbol, {})
    
    # Базовый расчет
    position_size = max_volume_usd / signal.entry_price
    
    # Применяем ограничения биржи с None-safe проверками
    precision = market_info.get('precision', {})
    amount_precision = precision.get('amount', 0.001)
    
    limits = market_info.get('limits', {})
    amount_limits = limits.get('amount', {})
    min_amount = amount_limits.get('min', amount_precision)
    max_amount = amount_limits.get('max', float('inf'))
    
    # None-safe проверки
    if min_amount is not None and position_size < min_amount:
        position_size = min_amount
    
    if max_amount is not None and position_size > max_amount:
        position_size = max_amount
```

### 3. Правильный вызов CCXT create_order
```python
# Подготавливаем параметры для BingX hedge mode
order_params = {
    'positionSide': 'LONG' if signal.direction == SignalDirection.LONG else 'SHORT'
}

# Правильный синтаксис CCXT
order_result = self.exchange.create_order(symbol, 'market', side, position_size, None, order_params)
```

### 4. Реальное размещение TP/SL ордеров
```python
# Размещение стоп лосса на BingX
order_params = {
    'positionSide': 'LONG' if trade.signal.direction == SignalDirection.LONG else 'SHORT',
    'stopPrice': stop_price,
    'type': 'STOP_MARKET'
}

try:
    order_result = self.exchange.create_order(
        trade.entry_order.symbol,
        'stop',  # stop market
        side,
        trade.entry_order.filled_amount,
        None,  # price = None для market order
        order_params
    )
    
    await self._log_to_file(trade.id, f"🛡️ Стоп лосс размещен на бирже: {side.upper()} @ {stop_price} (ID: {order_result['id']})")
    
except Exception as e:
    # Резервное локальное сохранение
    await self._log_to_file(trade.id, f"⚠️ Стоп лосс сохранен локально (ошибка биржи): {side.upper()} @ {stop_price} - {str(e)}")

# Аналогично для take profit с типом 'takeProfit'
```

## Результаты тестирования

### ✅ Проблемы с символами решены:
- `QNTUSDT` → `QNT/USDT:USDT` (swap)
- `HIFIUSDT` → `HIFI/USDT:USDT` (swap) 
- `LINKUSDT` → `LINK/USDT:USDT` (swap)
- `DOGEUSDT` → `DOGE/USDT:USDT` (swap)
- `SUPERUSDT` → `SUPER/USDT:USDT` (swap)

### ✅ API разрешения решены:
- Исключены все spot-рынки
- Используются только фьючерсы, совместимые с API ключом

### ✅ Hedge mode решен:
- Добавлен параметр `positionSide: LONG/SHORT`
- Ордера корректно размещаются в hedge режиме

### ✅ Минимальный размер решен:
- Автоматическая корректировка размера позиции
- Соблюдение минимальных требований биржи

### ✅ None-safe операции решены:
- Проверки `if min_amount is not None` предотвращают TypeError
- Проверки `if max_amount is not None` предотвращают TypeError

### ✅ CCXT синтаксис исправлен:
- Правильный вызов `create_order()` вместо `create_market_order()`
- Корректная передача параметров hedge mode

### ✅ TP/SL ордера решены:
- Реальное размещение TP/SL ордеров на BingX
- Детальное логирование процесса создания TP/SL
- Поддержка hedge mode для всех типов ордеров
- Резервное локальное сохранение при ошибках биржи

## Статус: ПОЛНОСТЬЮ РЕШЕНО ✅

Все СЕМЬ проблем успешно исправлены:
1. ❌ Symbol not found → ✅ Правильная нормализация символов
2. ❌ Permission denied → ✅ Только фьючерсы, никаких spot
3. ❌ Hedge mode error → ✅ Добавлен positionSide параметр  
4. ❌ Minimum amount → ✅ Автоматическая корректировка размера
5. ❌ NoneType comparison → ✅ None-safe проверки
6. ❌ Wrong argument count → ✅ Правильный CCXT синтаксис
7. ❌ TP/SL only local → ✅ **Реальное размещение TP/SL ордеров на BingX**

Бот теперь полностью совместим с BingX API в hedge режиме на фьючерсных рынках с полной функциональностью TP/SL. 