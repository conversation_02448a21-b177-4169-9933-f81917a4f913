# Торговая система

Автоматическая торговая система для работы с сигналами из Telegram каналов через биржу BingX.

## Возможности

- 🤖 Автоматическое выполнение торговых сигналов
- 📊 Поддержка только USD бессрочных фьючерсов (Perp)
- 🎯 Автоматическая установка стоп-лосса и тейк-профитов
- 📈 Перемещение стоп-лосса на безубыток после первого тейк-профита
- 📝 Подробное логирование в файлы и чат
- ⚙️ Гибкие настройки для каждого канала
- 🔧 Три типа стратегий: default, aggressive, conservative

## Архитектура

### Основные компоненты

1. **TradingSignal** (`signal_analyzer.py`) - обновленная схема сигнала с обязательными полями
2. **BingXTradingBot** (`trading_bot.py`) - торговый бот для работы с BingX
3. **TradingBotManager** (`bot_manager.py`) - менеджер ботов
4. **TradingIntegration** (`trading_integration.py`) - интеграция с основным парсером

### Схема торгового сигнала

```python
class TradingSignal(BaseModel):
    # Обязательные поля
    direction: SignalDirection      # Long/Short
    ticker: str                    # BTC/USDT, ETH/USDT
    entry_price: float            # Цена входа
    
    # Опциональные поля
    leverage: Optional[int] = None           # Плечо (1-100x)
    take_profits: Optional[List[float]] = None  # Уровни тейков
    stop_loss: Optional[float] = None        # Уровень стоп-лосса
    deposit_percentage: Optional[float] = None  # % от депозита
    
    # Служебные поля
    timestamp: Optional[str] = None
    timestamp_msk: Optional[str] = None
    channel: Optional[str] = None
```

## Настройка

### Переменные окружения

Добавьте в ваш `.env` файл:

```bash
# Торговая система
TRADING_ENABLED=False
BINGX_API_KEY=your_api_key_here
BINGX_API_SECRET=your_api_secret_here
BINGX_TESTNET=True

# Дополнительные конфигурации ботов (опционально, JSON)
# ⚠️ УСТАРЕЛО: Основные настройки теперь берутся из SOURCE_CHANNELS в index_v3.py
# TRADING_BOT_CONFIGS={"Канал": {"max_volume": 20, "leverage": 12}}
```

### Установка зависимостей

```bash
pip install ccxt>=4.0.0
```

### Конфигурация каналов

**🎯 Основной способ настройки** - торговые настройки хранятся прямо в `SOURCE_CHANNELS` в файле `index_v3.py`. Для каждого канала с `signal_fn: "signal_analyzer"` можно указать:

```python
"Название канала": {
    "id": -1001234567890,
    "forward_type": "custom",
    "signal_fn": "signal_analyzer", 
    "signals_only": True,
    "max_volume": 10,             # Объем позиции в USD
    "leverage": 10,               # Плечо для торговли (5x-20x)
    "portfolio_percent": 2.0,     # Процент от портфеля (приоритет над max_volume)
    "open_mode": "default"        # Режим открытия: "default" или "grid_open"
}
```

Система автоматически создает торговых ботов для всех таких каналов. Все настройки (leverage, объемы, режимы) берутся из `SOURCE_CHANNELS`.

### Настройки торговых ботов по каналам:

| Канал | Макс. объем | Плечо | Статус |
|-------|-------------|-------|---------|
| **Основные проверенные каналы** |
| Акулы рынка | $10 | 10x | ✅ Активен |
| Хозяин Биржи \| Даня | $10 | 10x | ✅ Активен |
| Хозяин Биржи | $10 | 10x | ✅ Активен |
| GotBit Trades | $10 | 15x | ✅ Активен |
| PREMIUM CRYPTO | $10 | 10x | ✅ Активен |
| Скальпинг с Андреем Павловым | $10 | 5x | ✅ Активен |
| Боря Бинг | $10 | 10x | ✅ Активен |
| Школа им. Влада | $10 | 8x | ✅ Активен |
| Дом Культуры имени Лебедева | $10 | 10x | ✅ Активен |
| Товарищ Ганн | $10 | 12x | ✅ Активен |
| Crypto Yoda Channel | $10 | 10x | ✅ Активен |
| **Новые публичные каналы** |
| Romanov Trade | $10 | 10x | 🔄 Настройка |
| Trader signals RU | $10 | 8x | 🔄 Настройка |
| Learn 2 Trade Crypto | $10 | 5x | 🔄 Настройка |
| Whale Trade🐋 \| Данил Ткачев | $10 | 12x | 🔄 Настройка |
| Agapov Trade - 222X | $10 | 15x | 🔄 Настройка |

## Настройки торговли

### Единая стратегия "default"
Все боты используют одну торговую логику с индивидуальными параметрами:
- **Объем позиции**: фиксированный для каждого канала ($10)
- **Плечо**: индивидуальное для каждого источника
- **Стоп-лосс**: -5% от входа
- **Тейк-профиты**: +1%, +2%, +3%

### Настройки плеча по каналам:
- **5x**: Консервативное (Скальпинг с Андреем Павловым, Learn 2 Trade Crypto)
- **8x**: Умеренное (Школа им. Влада, Trader signals RU)
- **10x**: Стандартное (большинство каналов)
- **12x**: Повышенное (Товарищ Ганн, Whale Trade)
- **15x**: Агрессивное (GotBit Trades, Agapov Trade - 222X)

## Торговая логика

### Вход в позицию
1. Получен сигнал с обязательными полями (direction, ticker, entry_price)
2. Проверка доступности бота для канала
3. Установка индивидуального плеча для канала (5x-15x)
4. Размещение рыночного ордера

### Управление позицией
1. **Стоп-лосс**: автоматически -5% от входа (или из сигнала)
2. **Тейк-профиты**: 3 уровня равными частями
   - Из сигнала (если есть) или автоматические: +1%, +2%, +3%
3. **Безубыток**: после первого тейк-профита стоп перемещается на цену входа

### Логирование

#### В чат
- Новые сделки
- Выполнение тейк-профитов и стоп-лоссов
- Перемещение на безубыток
- Закрытие позиций

#### В файлы
- `./bots/<trade_id>-log.md` - подробный лог каждой сделки
- `bots.md` - общая статистика ботов и активные сделки

## Мониторинг

### Файл bots.md
Автоматически обновляемый файл с информацией о:
- Конфигурациях ботов
- Статистике торговли
- Активных сделках
- Общем PnL

### Логи сделок
Каждая сделка создает отдельный markdown файл с полной историей:
- Время создания
- Параметры сигнала
- Выполненные ордера
- Изменения позиции
- Результат закрытия

## Безопасность

### Тестовая среда
По умолчанию система работает в тестовой среде BingX (`BINGX_TESTNET=True`)

### Ограничения объемов
Максимальные объемы настраиваются для каждого канала отдельно

### Управление рисками
- Обязательные стоп-лоссы
- Автоматическое перемещение на безубыток
- Индивидуальное плечо для каждого канала (5x-15x)

## Использование

### Автоматический режим
Система автоматически обрабатывает сигналы при работе основного парсера:

```python
# В index_v3.py автоматически вызывается:
if signal and trading_integration and trading_integration.is_trading_enabled():
    await trading_integration.process_trading_signal(signal, channel_name)
```

### Ручное управление
```python
# Отключить торговлю для канала
await trading_integration.disable_trading_for_channel("Акулы рынка")

# Включить торговлю для канала  
await trading_integration.enable_trading_for_channel("Акулы рынка")

# Получить статистику
stats = await trading_integration.get_trading_stats()
```

## Примеры конфигураций

### Настройка в SOURCE_CHANNELS (основной способ)
```python
SOURCE_CHANNELS = {
    "Новый канал": {
        "id": -1001234567890,
        "forward_type": "custom",
        "signal_fn": "signal_analyzer", 
        "signals_only": True,
        "max_volume": 15,     # Объем позиции в USD
        "leverage": 8         # Плечо 8x
    }
}
```

### Дополнительная настройка через переменные окружения (устарело)
```bash
# ⚠️ УСТАРЕЛО: Лучше настраивать прямо в SOURCE_CHANNELS
# Переопределить настройки для существующих каналов
# TRADING_BOT_CONFIGS='{"Акулы рынка": {"max_volume": 20, "leverage": 12}}'
```

**Рекомендуется** использовать основной способ через `SOURCE_CHANNELS` для лучшей читаемости и централизованного управления.

## Мониторинг и отладка

### Уровни логирования
- `ADMIN_LOG_LEVEL=INFO` - для подробных логов торговли
- `ADMIN_LOG_LEVEL=ERROR` - только ошибки

### Проверка статуса
Система логирует:
- ✅ Успешные операции зеленым
- ⚠️ Предупреждения желтым  
- ❌ Ошибки красным
- 🤖 Торговые операции с роботом

## Планы развития

- [ ] Поддержка частичного закрытия позиций
- [ ] Трейлинг стоп-лоссы
- [ ] Интеграция с другими биржами
- [ ] Бэктестинг стратегий
- [ ] Web интерфейс для управления

## Предупреждения

⚠️ **Риски торговли**
- Торговля криптовалютами связана с высокими рисками
- Всегда тестируйте на тестовой среде перед реальной торговлей
- Не используйте деньги, которые не можете позволить себе потерять

⚠️ **Технические риски**
- Система находится в стадии разработки
- Возможны сбои в работе API биржи
- Требуется постоянный мониторинг работы

## Поддержка

При возникновении проблем:
1. Проверьте логи в файлах `./bots/`
2. Убедитесь в правильности API ключей
3. Проверьте подключение к интернету
4. Обратитесь к разработчику с описанием проблемы 