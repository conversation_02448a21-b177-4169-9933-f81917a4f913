#!/usr/bin/env python3
"""
API Client для интеграции telegram-parser с Django API.
"""

import asyncio
import aiohttp
import json
from typing import Optional, List, Dict, Any
from dataclasses import dataclass


@dataclass
class Channel:
    """Модель канала из API."""
    id: int
    name: str
    telegram_id: str
    last_message_id: int
    leverage: Optional[int] = None
    portfolio_percent: Optional[float] = None
    wins: Optional[int] = None
    fails: Optional[int] = None
    wins_ratio: Optional[float] = None


class TradingAPIClient:
    """Клиент для работы с Trading API."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.api_url = f"{base_url}/apix/trading_signals"
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        """Асинхронный контекстный менеджер."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Закрытие сессии."""
        if self.session:
            await self.session.close()
    
    async def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Выполняет HTTP запрос к API."""
        if not self.session:
            raise RuntimeError("Session not initialized. Use async context manager.")
        
        url = f"{self.api_url}/{endpoint}"
        async with self.session.request(method, url, **kwargs) as response:
            response.raise_for_status()
            return await response.json()
    
    async def get_channels(self, page: int = 1, page_size: int = 100) -> List[Channel]:
        """Получает список каналов."""
        data = await self._make_request(
            "GET", 
            "channels/", 
            params={"page": page, "page_size": page_size}
        )
        
        channels = []
        for channel_data in data.get("results", []):
            channel = Channel(
                id=channel_data["id"],
                name=channel_data["name"],
                telegram_id=channel_data["telegram_id"],
                last_message_id=channel_data.get("last_message_id", 0),
                leverage=channel_data.get("leverage"),
                portfolio_percent=channel_data.get("portfolio_percent"),
                wins=channel_data.get("wins"),
                fails=channel_data.get("fails"),
                wins_ratio=channel_data.get("wins_ratio"),
            )
            channels.append(channel)
        
        return channels
    
    async def get_channel_by_name(self, channel_name: str) -> Optional[Channel]:
        """Получает канал по имени."""
        try:
            # Получаем все каналы и ищем по имени
            channels = await self.get_channels(page_size=1000)
            for channel in channels:
                if channel.name == channel_name:
                    return channel
            return None
        except Exception as e:
            print(f"Ошибка при поиске канала {channel_name}: {e}")
            return None
    
    async def get_channel_by_telegram_id(self, telegram_id: str) -> Optional[Channel]:
        """Получает канал по Telegram ID."""
        try:
            channels = await self.get_channels(page_size=1000)
            for channel in channels:
                if channel.telegram_id == telegram_id:
                    return channel
            return None
        except Exception as e:
            print(f"Ошибка при поиске канала по Telegram ID {telegram_id}: {e}")
            return None
    
    async def update_channel_last_message_id(self, channel_id: int, last_message_id: int) -> Optional[Channel]:
        """Обновляет last_message_id для канала."""
        try:
            data = await self._make_request(
                "PATCH",
                f"channels/{channel_id}/",
                json={"last_message_id": last_message_id}
            )
            
            return Channel(
                id=data["id"],
                name=data["name"],
                telegram_id=data["telegram_id"],
                last_message_id=data.get("last_message_id", 0),
                leverage=data.get("leverage"),
                portfolio_percent=data.get("portfolio_percent"),
                wins=data.get("wins"),
                fails=data.get("fails"),
                wins_ratio=data.get("wins_ratio"),
            )
        except Exception as e:
            print(f"Ошибка при обновлении канала {channel_id}: {e}")
            return None
    
    async def update_channel_stats(self, channel_id: int, stats_data: dict) -> Optional[Channel]:
        """Обновляет статистику канала."""
        try:
            data = await self._make_request(
                "PATCH",
                f"channels/{channel_id}/",
                json=stats_data
            )
            
            return Channel(
                id=data["id"],
                name=data["name"],
                telegram_id=data["telegram_id"],
                last_message_id=data.get("last_message_id", 0),
                leverage=data.get("leverage"),
                portfolio_percent=data.get("portfolio_percent"),
                wins=data.get("wins"),
                fails=data.get("fails"),
                wins_ratio=data.get("wins_ratio"),
            )
        except Exception as e:
            print(f"Ошибка при обновлении статистики канала {channel_id}: {e}")
            return None
    
    async def get_channels_with_last_message_id(self) -> List[Channel]:
        """Получает каналы с last_message_id > 0."""
        try:
            data = await self._make_request(
                "GET", 
                "channels/", 
                params={"last_message_id__gt": 0, "page_size": 1000}
            )
            
            channels = []
            for channel_data in data.get("results", []):
                channel = Channel(
                    id=channel_data["id"],
                    name=channel_data["name"],
                    telegram_id=channel_data["telegram_id"],
                    last_message_id=channel_data.get("last_message_id", 0),
                    leverage=channel_data.get("leverage"),
                    portfolio_percent=channel_data.get("portfolio_percent"),
                    wins=channel_data.get("wins"),
                    fails=channel_data.get("fails"),
                    wins_ratio=channel_data.get("wins_ratio"),
                )
                channels.append(channel)
            
            return channels
        except Exception as e:
            print(f"Ошибка при получении каналов с last_message_id: {e}")
            return []


async def test_api_client():
    """Тестирует API клиент."""
    async with TradingAPIClient() as client:
        try:
            print("🔍 Тестирование API клиента...")
            
            # Получаем каналы
            channels = await client.get_channels(page_size=5)
            print(f"📊 Найдено {len(channels)} каналов:")
            
            for channel in channels:
                print(f"  - {channel.name}: last_message_id = {channel.last_message_id}")
            
            # Получаем каналы с last_message_id > 0
            active_channels = await client.get_channels_with_last_message_id()
            print(f"\n🎯 Активных каналов (last_message_id > 0): {len(active_channels)}")
            
            # Тестируем обновление
            if channels:
                first_channel = channels[0]
                print(f"\n🔄 Обновляем {first_channel.name}...")
                updated = await client.update_channel_last_message_id(
                    first_channel.id, 
                    first_channel.last_message_id + 100
                )
                if updated:
                    print(f"✅ Обновлено: {updated.name} -> {updated.last_message_id}")
            
            print("\n🎉 API клиент работает корректно!")
            
        except Exception as e:
            print(f"❌ Ошибка тестирования API клиента: {e}")


if __name__ == "__main__":
    asyncio.run(test_api_client()) 