# Время жизни позиций (Position Lifetime)

## 🎯 Описание

Функция времени жизни позиций позволяет автоматически закрывать торговые позиции через заданное время, независимо от срабатывания стоп-лосса или тейк-профитов. Это полезно для ограничения времени экспозиции рынку и управления рисками.

## ⚙️ Настройка

### 1. Добавление параметра в SOURCE_CHANNELS

```python
SOURCE_CHANNELS = {
    "Канал с ограничением времени": {
        "id": -1001234567890,
        "forward_type": "custom",
        "signal_fn": "signal_analyzer",
        "signals_only": True,
        "leverage": 10,
        "portfolio_percent": 2.0,
        "position_lifetime": "2h30m"  # ← Позиция закроется через 2 часа 30 минут
    },
    "Скальпинг канал": {
        "id": -1001987654321,
        "forward_type": "custom", 
        "signal_fn": "signal_analyzer",
        "signals_only": True,
        "leverage": 15,
        "portfolio_percent": 1.0,
        "position_lifetime": "45m"  # ← Позиция закроется через 45 минут
    },
    "Канал без ограничения": {
        "id": -1001555666777,
        "forward_type": "custom",
        "signal_fn": "signal_analyzer", 
        "signals_only": True,
        "leverage": 10,
        "portfolio_percent": 1.5
        # position_lifetime не указан = "0s" (отключено)
    }
}
```

## 📝 Поддерживаемые форматы времени

| Формат | Описание | Секунды |
|---------|----------|---------|
| `"0s"` | Отключено (по умолчанию) | 0 |
| `"30s"` | 30 секунд | 30 |
| `"5m"` | 5 минут | 300 |
| `"1h"` | 1 час | 3600 |
| `"2h30m"` | 2 часа 30 минут | 9000 |
| `"1h30m10s"` | 1 час 30 минут 10 секунд | 5410 |
| `"45m30s"` | 45 минут 30 секунд | 2730 |

## 🔧 Как это работает

### 1. Установка времени жизни
При создании позиции система:
- Парсит строку времени в секунды
- Сохраняет в `TradeInstance.position_lifetime_seconds`
- Логирует информацию о времени жизни

### 2. Мониторинг позиций
Каждые 10 секунд система проверяет:
- Текущее время vs время создания позиции
- Если время жизни истекло → инициируется закрытие

### 3. Умная проверка и закрытие
При истечении времени:
1. **Проверка статуса** - проверяется, что позиция действительно еще открыта на бирже
2. **Проверка ордеров** - проверяются статусы всех связанных ордеров перед отменой
3. **Отмена ордеров** - отменяются только активные SL и TP ордера
4. **Закрытие позиции** - размещается рыночный ордер на закрытие (только если позиция открыта)
5. **Логирование** - подробная информация в файл и чат
6. **Уведомления** - сообщение в TARGET телеграм канал

## 📊 Примеры использования

### Скальпинг (быстрые сделки)
```python
"Скальпинг канал": {
    "position_lifetime": "15m",  # Максимум 15 минут в рынке
    "leverage": 20,
    "portfolio_percent": 0.5
}
```

### Дневная торговля
```python
"Дневные сигналы": {
    "position_lifetime": "4h",   # Максимум 4 часа в рынке
    "leverage": 10,
    "portfolio_percent": 2.0
}
```

### Долгосрочные позиции
```python
"Долгосрочные сигналы": {
    "position_lifetime": "24h",  # Максимум 24 часа в рынке
    "leverage": 5,
    "portfolio_percent": 3.0
}
```

### Тестовый режим
```python
"Тест": {
    "position_lifetime": "30s",  # Быстрое тестирование
    "leverage": 5,
    "portfolio_percent": 0.1
}
```

## 🔍 Логирование и мониторинг

### В файле сделки (`./bots/{trade_id}-log.md`)
```markdown
**Время жизни позиции:** 2h30m (автозакрытие)

...

⏰ Время жизни позиции истекло: 2h30m5s >= 2h30m
🔍 Позиция найдена: BTC/USDT:USDT LONG
📊 Размер изменился: 0.5 → 0.3
✅ Позиция подтверждена открытой на бирже - закрываем принудительно
📊 Используем актуальный размер с биржи: 0.3 (было 0.5)
ℹ️ Ордер take_profit_1 уже filled: 1234567891
🗑️ Отменен stop_loss ордер: 1234567890
🔒 Позиция закрыта рыночным ордером: SELL 0.3 (ID: 1234567892)
✅ Позиция принудительно закрыта по истечении времени жизни
```

### Альтернативный сценарий (позиция уже закрыта)
```markdown
⏰ Время жизни позиции истекло: 2h30m5s >= 2h30m
🔍 Позиция BTC/USDT:USDT LONG не найдена среди открытых на бирже
ℹ️ Позиция уже закрыта на бирже - обновляем локальный статус
ℹ️ Сделка помечена как закрытая: Позиция уже закрыта на бирже
```

### В Telegram чате
**Сценарий 1: Позиция была открыта**
```
⏰ Позиция trade_12345 закрыта по таймауту: 2h30m

🔒 **СДЕЛКА ЗАКРЫТА**
**ID сделки:** trade_12345
**Канал:** Тестовый канал
**Сигнал:** LONG BTC/USDT
**Причина закрытия:** Истечение времени жизни
**Длительность:** 2:30:05
💰 **Текущий баланс USDT:** $1,234.56
```

**Сценарий 2: Позиция уже была закрыта**
```
ℹ️ Позиция trade_12345 уже была закрыта (время жизни: 2h30m)

🔒 **СДЕЛКА ЗАКРЫТА**
**ID сделки:** trade_12345
**Канал:** Тестовый канал
**Сигнал:** LONG BTC/USDT
**Причина закрытия:** Позиция уже закрыта на бирже
**Длительность:** 2:30:05
💰 **Текущий баланс USDT:** $1,234.56
```

## ⚠️ Важные особенности

### 1. Приоритет над TP/SL
- Время жизни имеет высший приоритет
- Даже если TP/SL не сработали, позиция закроется по времени

### 2. Умная проверка состояния
- Перед закрытием проверяется актуальный статус позиции на бирже
- Если позиция уже закрыта - избегается лишних действий
- Консервативный подход при ошибках проверки

### 3. Рыночное закрытие
- Используется рыночный ордер для гарантированного исполнения (только если позиция открыта)
- Может быть небольшое проскальзывание цены

### 4. Отмена ордеров с проверкой
- Проверяется статус каждого ордера перед отменой
- Отменяются только активные ордера
- Предотвращает ошибки при отмене уже исполненных ордеров

### 5. Точность времени
- Проверка каждые 10 секунд
- Возможна погрешность до 10 секунд

## 🎯 Стратегии применения

### По типу торговли

| Тип торговли | Рекомендуемое время | Обоснование |
|-------------|-------------------|-------------|
| **Скальпинг** | 5m - 30m | Быстрые движения, минимальная экспозиция |
| **Внутридневная торговля** | 2h - 8h | Дневные тренды и паттерны |
| **Свинг торговля** | 12h - 48h | Многодневные движения |
| **Тестирование** | 30s - 5m | Быстрая проверка стратегий |

### По волатильности рынка

| Волатильность | Время жизни | Причина |
|--------------|-------------|---------|
| **Высокая** | Короче | Быстрые изменения, высокий риск |
| **Низкая** | Дольше | Медленные движения, нужно время |
| **Неопределенная** | Средняя | Компромисс между риском и возможностью |

## 🚀 Интеграция с другими функциями

### Совместимость с существующими параметрами
- ✅ `leverage` - работает как обычно
- ✅ `portfolio_percent` - работает как обычно  
- ✅ `open_mode` - все режимы поддерживаются
- ✅ `move_stop_to_breakeven` - работает до истечения времени
- ✅ `max_profit` - работает до истечения времени

### Логика взаимодействия
1. **Время жизни > 0** → активируется мониторинг
2. **TP/SL срабатывает первым** → обычное закрытие
3. **Время истекает первым** → принудительное закрытие
4. **Время = 0s** → функция отключена, работает как раньше

## 📈 Примеры конфигураций

### Агрессивный скальпинг
```python
"Быстрый скальпинг": {
    "id": -1001234567890,
    "forward_type": "custom",
    "signal_fn": "signal_analyzer",
    "signals_only": True,
    "leverage": 25,
    "portfolio_percent": 0.5,
    "position_lifetime": "10m",  # Очень быстро
    "move_stop_to_breakeven": True
}
```

### Консервативная торговля
```python
"Консервативные сигналы": {
    "id": -1001987654321,
    "forward_type": "custom",
    "signal_fn": "signal_analyzer", 
    "signals_only": True,
    "leverage": 5,
    "portfolio_percent": 3.0,
    "position_lifetime": "6h",   # Достаточно времени
    "move_stop_to_breakeven": True
}
```

### Экспериментальная настройка
```python
"Новый канал": {
    "id": -1001555666777,
    "forward_type": "custom",
    "signal_fn": "signal_analyzer",
    "signals_only": True, 
    "leverage": 10,
    "portfolio_percent": 1.0,
    "position_lifetime": "1h30m", # Тестируем оптимальное время
    "move_stop_to_breakeven": True
}
```

## 🔧 Отладка и устранение неполадок

### Проверка работы функции
1. **Проверьте логи** - в файле сделки должна быть строка "Время жизни позиции"
2. **Проверьте формат** - убедитесь, что время указано корректно
3. **Проверьте мониторинг** - функция monitor_trades() должна работать

### Часто встречающиеся проблемы

| Проблема | Причина | Решение |
|----------|---------|---------|
| Время не работает | Неверный формат | Используйте формат "1h30m" |
| Позиция не закрывается | monitor_trades() не запущен | Проверьте инициализацию бота |
| Ошибки в логах | Проблемы с биржей | Проверьте подключение к BingX |

## ✅ Готово к использованию

Функция полностью реализована и интегрирована в торговую систему. Для активации просто добавьте параметр `position_lifetime` в конфигурацию любого торгового канала в `SOURCE_CHANNELS`. 