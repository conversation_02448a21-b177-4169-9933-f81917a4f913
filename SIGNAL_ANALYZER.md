# Анализатор торговых сигналов

## 🎯 Описание

Анализатор торговых сигналов - это дополнительная функция для извлечения структурированной информации о торговых сигналах из сообщений Telegram. Использует платные AI модели (OpenAI/Claude) для высокой точности распознавания.

## ⚙️ Настройка

### 1. Добавить в конфигурацию канала

```python
SOURCE_CHANNELS = {
    "Акулы рынка": {
        "id": -1002667675217, 
        "forward_type": "custom", 
        "signal_fn": "signal_analyzer",  # ← Включить анализ сигналов
        "signals_only": True  # ← Пересылать ТОЛЬКО сигналы на вход
    },
}
```

### 2. Переменные окружения

Использует тот же `OPENROUTER_API_KEY` что и анализатор новостей:

```env
# Обязательно для работы анализатора сигналов
OPENROUTER_API_KEY=your_api_key_here
```

## 🤖 Поддерживаемые модели

Анализатор использует **платные модели** для максимальной точности:

| Модель | Цена Input | Цена Output | Приоритет |
|--------|------------|-------------|-----------|
| `openai/gpt-4o-mini` | $0.15/1M | $0.6/1M | ⭐⭐⭐ |
| `openai/gpt-4o` | $2.5/1M | $10/1M | ⭐⭐ |
| `anthropic/claude-3.5-sonnet` | $3/1M | $15/1M | ⭐⭐ |
| `google/gemini-pro-1.5` | $1.25/1M | $5/1M | ⭐ |

## 🎯 Опция signals_only

Новая опция `"signals_only": True` позволяет пересылать **ТОЛЬКО** сообщения с торговыми сигналами на вход, игнорируя все остальные сообщения из канала.

### 🚫 Что НЕ пересылается с signals_only=True:
- Сообщения о закрытии позиций ("Взяли 1-ый тейк")
- Обновления стоп-лоссов ("Стоп ставим в б/у")
- Аналитические посты без конкретных сигналов
- Общие новости и комментарии
- Рекламные сообщения

### ✅ Что пересылается с signals_only=True:
- Новые торговые сигналы с точкой входа
- Сообщения с указанием направления (Long/Short)
- Сигналы с указанием плеча, тейков и стопов

```python
# Пример настройки
"Акулы рынка": {
    "id": -1002667675217,
    "forward_type": "custom",
    "signal_fn": "signal_analyzer",
    "signals_only": True  # Только входы в позицию
}
```

## 📊 Извлекаемые данные

### Структура торгового сигнала:

**Важно:** 
- `timestamp` - время публикации в UTC (оригинальное время Telegram)
- `timestamp_msk` - время публикации в московском часовом поясе (UTC+3)
- `channel` - название канала-источника сигнала

```json
{
  "direction": "Short",           // "Long" или "Short"
  "ticker": "UNI/USDT",          // Торговая пара
  "leverage": 15,                 // Плечо (1-100x)
  "entry_price": 7.110,          // Цена входа
  "take_profits": [7.003, 6.795, 6.469], // Уровни взятия прибыли
  "stop_loss": 7.356,            // Стоп-лосс
  "deposit_percentage": 2.0,      // Процент от депозита
  "timestamp": "2025-01-16T10:30:00",  // Время публикации (UTC)
  "timestamp_msk": "2025-01-16T13:30:00",  // Время публикации (МСК, UTC+3)
  "channel": "Акулы рынка"       // Название канала-источника
}
```

### Примеры распознавания:

**Исходный текст:**
```
🖼 Short📉 UNI/USDT

Плечо: 15x
Вход: 7.110 / по рынку
Тейки: 7.003 / 6.795 / 6.469
Стоп: 7.356

До 2% от депозита!
```

**Извлеченный JSON:**
```json
{
  "direction": "Short",
  "ticker": "UNI/USDT", 
  "leverage": 15,
  "entry_price": 7.110,
  "take_profits": [7.003, 6.795, 6.469],
  "stop_loss": 7.356,
  "deposit_percentage": 2.0,
  "timestamp": "2025-01-16T15:30:45.123",
  "timestamp_msk": "2025-01-16T18:30:45.123",
  "channel": "Акулы рынка"
}
```

## 📝 Интеграция в сообщения

Когда анализатор находит торговый сигнал, JSON добавляется к пересылаемому сообщению:

```
📢 Источник: Акулы рынка
──────────

🖼 Short📉 UNI/USDT

Плечо: 15x
Вход: 7.110 / по рынку
[... остальной текст ...]

──────────
📈 Тикеры: UNI
🏷️ Теги: cryptocurrency, trading
💭 Настроение: 🟡 Нейтральное (0.00)

──────────
**📊 Торговый сигнал (JSON):**
```json
{
  "direction": "Short",
  "ticker": "UNI/USDT",
  "leverage": 15,
  "entry_price": 7.11,
  "take_profits": [7.003, 6.795, 6.469],
  "stop_loss": 7.356,
  "deposit_percentage": 2.0,
  "timestamp": "2025-01-16T15:30:45.123",
  "timestamp_msk": "2025-01-16T18:30:45.123",
  "channel": "Акулы рынка"
}
```

## 🔍 Логирование

При работе анализатора в логах появляются записи:

```
🎯 Начинаем извлечение торгового сигнала из сообщения 342932 из Акулы рынка
INFO: Пробуем извлечь сигнал с моделью: openai/gpt-4o-mini
SUCCESS: 🎯 Торговый сигнал извлечен из Акулы рынка (ID: 342932): 📉 Short UNI/USDT, Плечо: 15x, Вход: 7.11
```

При инициализации:
```
SUCCESS: Анализатор торговых сигналов успешно инициализирован для каналов: Акулы рынка
⚙️ Настройки каналов:
   • Акулы рынка: кастомная обработка + анализ сигналов
```

## 💰 Стоимость использования

**Примерные расходы на gpt-4o-mini:**
- Анализ одного сигнала (~200 токенов): $0.0001
- 1000 сигналов в месяц: ~$0.10
- Очень экономично для реальной торговли

## ⚠️ Важные замечания

1. **Только для "custom" режима:** Анализ сигналов работает только с `forward_type: "custom"`
2. **Автоматическая инициализация:** Анализатор создается только при наличии каналов с `signal_fn: "signal_analyzer"`
3. **Fallback модели:** Если одна модель недоступна, автоматически пробует следующую
4. **Валидация данных:** Все извлеченные данные проверяются через Pydantic схему
5. **Пропуск не-торговых сообщений:** Если сообщение не содержит сигнала, анализатор возвращает `null`
6. **Фильтрация сигналов:** С опцией `"signals_only": True` пересылаются только сообщения с торговыми сигналами на вход

## 🚀 Примеры использования

### Базовая настройка
```python
SOURCE_CHANNELS = {
    "Торговые сигналы": {
        "id": -1001234567890,
        "forward_type": "custom",
        "signal_fn": "signal_analyzer"
    }
}
```

### Смешанная настройка
```python
SOURCE_CHANNELS = {
    # Канал с сигналами - извлекаем JSON, только сигналы на вход
    "Акулы рынка": {
        "id": -1002667675217, 
        "forward_type": "custom", 
        "signal_fn": "signal_analyzer",
        "signals_only": True  # Только торговые сигналы на вход
    },
    
    # Канал с сигналами - извлекаем JSON, все сообщения
    "Premium Crypto": {
        "id": -1001737971552,
        "forward_type": "custom",
        "signal_fn": "signal_analyzer",
        "signals_only": False  # Все сообщения (включая обновления)
    },
    
    # Новостной канал - только анализ настроения
    "Crypto News": {
        "id": -1001111111111,
        "forward_type": "custom"
        # signal_fn не указан = обычная обработка
    },
    
    # Официальный канал - простой репост
    "Binance Announcements": {
        "id": -1001146915409,
        "forward_type": "repost"
        # signal_fn игнорируется в режиме repost
    }
}
```

## 🔧 Отладка

Если анализатор не работает:

1. **Проверьте API ключ:** `OPENROUTER_API_KEY` в .env
2. **Проверьте конфигурацию:** `signal_fn: "signal_analyzer"` в канале
3. **Проверьте режим:** `forward_type: "custom"` (не "repost")
4. **Проверьте логи:** Должно быть сообщение об инициализации
5. **Проверьте баланс:** Достаточно ли средств на OpenRouter

## 📈 Будущие улучшения

- [ ] Поддержка других бирж (не только USDT пары)
- [ ] Извлечение дополнительных параметров (Risk/Reward, время сигнала)
- [ ] Интеграция с торговыми API для автоматического исполнения
- [ ] Статистика успешности сигналов
- [ ] Уведомления о важных сигналах 