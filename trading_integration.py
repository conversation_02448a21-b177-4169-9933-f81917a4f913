import os
import json
import asyncio
from typing import Dict, Optional
from bot_manager import TradingBotManager
from signal_analyzer import TradingSignal

class TradingIntegration:
    """Интеграция торговой системы с парсером Telegram"""

    def __init__(self, logger=None):
        self.logger = logger
        self.bot_manager: Optional[TradingBotManager] = None
        self.trading_enabled = False

        # Настройки из переменных окружения
        self.api_key = os.getenv('BINGX_API_KEY')
        self.api_secret = os.getenv('BINGX_API_SECRET')
        self.testnet = os.getenv('BINGX_TESTNET', 'True').lower() == 'true'
        self.trading_enabled = os.getenv('TRADING_ENABLED', 'False').lower() == 'true'

        # Конфигурации ботов для каналов
        self.bot_configs = self._load_bot_configs()

    def _load_bot_configs(self) -> Dict[str, Dict]:
        """Загружает конфигурации ботов из SOURCE_CHANNELS"""
        try:
            # Импортируем функцию из index_v3
            from index_v3 import get_trading_channels_configs

            # Получаем конфигурации прямо из SOURCE_CHANNELS
            configs = get_trading_channels_configs()

            # Логируем результат для отладки
            print(f"[DEBUG] Загружено {len(configs)} торговых конфигураций: {list(configs.keys())}")

            # Дополнительные настройки из переменных окружения (если есть)
            configs_json = os.getenv('TRADING_BOT_CONFIGS', '{}')
            if configs_json.strip() and configs_json != '{}':
                env_configs = json.loads(configs_json)
                print(f"[DEBUG] Найдены дополнительные конфигурации из ENV: {list(env_configs.keys())}")
                # Объединяем с приоритетом пользовательских настроек
                for channel, env_config in env_configs.items():
                    if channel in configs:
                        configs[channel].update(env_config)
                    else:
                        configs[channel] = env_config

            return configs

        except Exception as e:
            print(f"[DEBUG] Ошибка загрузки конфигураций ботов: {str(e)}")
            if self.logger:
                asyncio.create_task(self.logger.error(f"Ошибка загрузки конфигураций ботов: {str(e)}", e))
            return {}

    async def _cleanup_bots_without_positions(self) -> Dict[str, Dict]:
        """Очищает конфигурации ботов без открытых позиций"""
        try:
            if not self.bot_manager or not self.bot_manager._exchange_initialized:
                if self.logger:
                    await self.logger.warning("⚠️ Не могу проверить позиции - менеджер ботов не инициализирован")
                return self.bot_configs

            # В тестовом режиме не удаляем ботов для безопасности
            if self.testnet:
                if self.logger:
                    await self.logger.info("🧪 Тестовый режим: очистка ботов отключена для безопасности")
                return self.bot_configs

            # Получаем открытые позиции с биржи
            if self.logger:
                await self.logger.info("🔍 Проверяем открытые позиции на бирже...")

            # Создаем временный бот для проверки позиций
            temp_bot = None
            try:
                from trading_bot import BingXTradingBot
                temp_bot = BingXTradingBot(self.api_key, self.api_secret, 10, self.testnet, self.logger)
                temp_bot.exchange = self.bot_manager._shared_exchange
                
                open_positions = await temp_bot._get_open_positions_from_exchange()
                
                if not open_positions:
                    if self.logger:
                        await self.logger.info("📊 Открытых позиций не найдено - проверяем активность ботов")
                    # Не удаляем всех ботов сразу, проверяем активность каждого

                # Извлекаем символы из открытых позиций
                position_symbols = set()
                for position in open_positions:
                    symbol = position.get('symbol', '')
                    if symbol:
                        # Нормализуем символ для сопоставления
                        base_symbol = symbol.replace('/USDT:USDT', '').replace('USDT', '').replace('/', '')
                        position_symbols.add(base_symbol.upper())

                if self.logger:
                    await self.logger.info(f"📊 Найдено {len(open_positions)} открытых позиций: {', '.join(position_symbols)}")

                # Проверяем активные сделки из файлов ботов
                active_channels = set()
                recent_channels = set()
                try:
                    import os
                    import glob
                    from datetime import datetime, timedelta
                    
                    # Проверяем конфигурации ботов из bots.md
                    if os.path.exists('./bots.md'):
                        try:
                            with open('./bots.md', 'r', encoding='utf-8') as f:
                                content = f.read()
                                # Парсим JSON конфигурации из файла
                                import json
                                import re
                                
                                # Ищем JSON блоки в файле
                                json_matches = re.findall(r'```json\n(.*?)\n```', content, re.DOTALL)
                                for json_str in json_matches:
                                    try:
                                        bot_data = json.loads(json_str)
                                        if isinstance(bot_data, dict) and 'source_channel' in bot_data:
                                            channel = bot_data['source_channel']
                                            if channel in self.bot_configs:
                                                # Проверяем время последней активности
                                                last_signal = bot_data.get('last_signal_at')
                                                if last_signal:
                                                    try:
                                                        last_time = datetime.fromisoformat(last_signal.replace('Z', '+00:00'))
                                                        # Если активность была в последние 24 часа - считаем недавней
                                                        if datetime.now() - last_time.replace(tzinfo=None) < timedelta(hours=24):
                                                            recent_channels.add(channel)
                                                    except:
                                                        pass
                                                
                                                # Проверяем общее количество сделок
                                                total_trades = bot_data.get('total_trades', 0)
                                                if total_trades > 0:
                                                    active_channels.add(channel)
                                    except:
                                        continue
                        except Exception as e:
                            if self.logger:
                                await self.logger.warning(f"⚠️ Не удалось прочитать bots.md: {str(e)}")
                    
                    # Ищем файлы активных сделок в папке bots/
                    if os.path.exists('./bots/'):
                        log_files = glob.glob('./bots/*-log.md')
                        for log_file in log_files:
                            try:
                                with open(log_file, 'r', encoding='utf-8') as f:
                                    content = f.read()
                                    # Ищем канал в логе
                                    for line in content.split('\n'):
                                        if line.startswith('**Канал:**'):
                                            channel_name = line.replace('**Канал:**', '').strip()
                                            if channel_name in self.bot_configs:
                                                active_channels.add(channel_name)
                                            break
                            except Exception as e:
                                # Игнорируем ошибки чтения отдельных файлов
                                pass
                    
                    if self.logger:
                        if active_channels:
                            await self.logger.info(f"📋 Найдены активные каналы: {', '.join(active_channels)}")
                        if recent_channels:
                            await self.logger.info(f"🕐 Недавно активные каналы (24ч): {', '.join(recent_channels)}")
                        
                except Exception as e:
                    if self.logger:
                        await self.logger.warning(f"⚠️ Не удалось проверить активные сделки: {str(e)}")

                # Фильтруем конфигурации ботов
                filtered_configs = {}
                removed_count = 0
                
                for channel, config in self.bot_configs.items():
                    # Проверяем, есть ли у канала активные позиции или сделки
                    has_position = False
                    reason = ""
                    
                    # Если есть активные сделки из логов - оставляем бота
                    if channel in active_channels:
                        has_position = True
                        reason = "есть активные сделки"
                    
                    # Если канал был недавно активен - оставляем бота (безопасность)
                    elif channel in recent_channels:
                        has_position = True
                        reason = "недавняя активность (24ч)"
                    
                    # Если нет открытых позиций вообще, но есть недавняя активность - оставляем
                    elif not open_positions and channel in recent_channels:
                        has_position = True
                        reason = "недавняя активность несмотря на отсутствие позиций"
                    
                    # Если нет позиций и нет активности - проверяем дополнительно
                    elif not open_positions and channel not in active_channels and channel not in recent_channels:
                        # Дополнительная проверка: если канал есть в SOURCE_CHANNELS - не удаляем
                        try:
                            from index_v3 import SOURCE_CHANNELS
                            if channel in SOURCE_CHANNELS:
                                has_position = True
                                reason = "канал в основной конфигурации SOURCE_CHANNELS"
                            else:
                                has_position = False
                                reason = "нет позиций, активности и не в SOURCE_CHANNELS"
                        except:
                            has_position = True
                            reason = "ошибка проверки SOURCE_CHANNELS - оставлен для безопасности"
                    
                    # По умолчанию оставляем бота (консервативный подход)
                    else:
                        has_position = True
                        reason = "оставлен по умолчанию (консервативный подход)"
                    
                    if has_position:
                        filtered_configs[channel] = config
                        if self.logger and reason:
                            await self.logger.info(f"✅ Канал {channel} сохранен: {reason}")
                    else:
                        removed_count += 1
                        if self.logger:
                            await self.logger.info(f"🗑️ Удален бот без позиций: {channel} ({reason})")

                if self.logger:
                    await self.logger.success(f"✅ Очистка завершена: оставлено {len(filtered_configs)} ботов, удалено {removed_count}")

                return filtered_configs

            finally:
                # Очищаем временный бот
                if temp_bot:
                    temp_bot.exchange = None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"❌ Ошибка при очистке ботов: {str(e)}", e)
            # В случае ошибки возвращаем исходные конфигурации
            return self.bot_configs

    async def initialize(self) -> bool:
        """Инициализирует торговую систему"""
        try:
            if self.logger:
                await self.logger.info(f"🤖 Инициализация торговой системы... Найдено {len(self.bot_configs)} конфигураций каналов")

            if not self.trading_enabled:
                if self.logger:
                    await self.logger.info("🤖 Торговая система отключена (TRADING_ENABLED=False)")
                    await self.logger.info("💡 Для включения торговли установите TRADING_ENABLED=True и настройте API ключи BingX в .env файле")
                return True

            if not self.api_key or not self.api_secret:
                if self.logger:
                    await self.logger.error("❌ API ключи BingX не настроены - торговля отключена")
                    await self.logger.error("💡 Добавьте BINGX_API_KEY и BINGX_API_SECRET в .env файл")
                return False

            # Создаем менеджер ботов
            self.bot_manager = TradingBotManager(
                api_key=self.api_key,
                api_secret=self.api_secret,
                testnet=self.testnet,
                logger=self.logger
            )

            # Инициализируем менеджер
            if not await self.bot_manager.initialize():
                if self.logger:
                    await self.logger.error("❌ Не удалось инициализировать менеджер торговых ботов")
                return False

            # Очищаем ботов без позиций
            self.bot_configs = await self._cleanup_bots_without_positions()

            if self.logger:
                await self.logger.info(f"🤖 После очистки осталось {len(self.bot_configs)} конфигураций ботов")

            # Регистрируем ботов для каналов
            registered_count = 0
            for channel, config in self.bot_configs.items():
                max_volume = config.get('max_volume', 50)
                leverage = config.get('leverage', 10)
                bot_function = config.get('bot_function', 'default')
                portfolio_percent = config.get('portfolio_percent', None)
                max_portfolio_usage = config.get('max_portfolio_usage', 100)  # Глобальное ограничение портфеля
                move_stop_to_breakeven = config.get('move_stop_to_breakeven', True)  # Перемещение стоп-лосса на безубыток
                max_take_profit_percent = config.get('max_profit_percent', 0.0)  # Максимальная прибыль в процентах

                # Импортируем OpenMode для конвертации строки в enum
                from trading_bot import OpenMode
                open_mode_str = config.get('open_mode', 'default')
                # Корректно обрабатываем все возможные значения OpenMode
                if open_mode_str == 'grid_open':
                    open_mode = OpenMode.GRID_OPEN
                elif open_mode_str == 'limit_open_1_limit_exit_5':
                    open_mode = OpenMode.LIMIT_OPEN_1_LIMIT_EXIT_5
                else:
                    open_mode = OpenMode.DEFAULT

                if await self.bot_manager.register_bot_for_source(channel, max_volume, leverage, bot_function, portfolio_percent, max_portfolio_usage, open_mode, move_stop_to_breakeven, max_take_profit_percent):
                    registered_count += 1
                    if self.logger:
                        if portfolio_percent is not None:
                            await self.logger.success(f"🤖 Бот зарегистрирован для {channel}: {portfolio_percent}% портфеля (макс. {max_portfolio_usage}% общего), плечо: {leverage}x, функция: {bot_function}, режим: {open_mode.value}")
                        else:
                            await self.logger.success(f"🤖 Бот зарегистрирован для {channel}: ${max_volume} (макс. {max_portfolio_usage}% общего), плечо: {leverage}x, функция: {bot_function}, режим: {open_mode.value}")
                else:
                    if self.logger:
                        await self.logger.warning(f"⚠️ Не удалось зарегистрировать бота для {channel}")

            if self.logger:
                await self.logger.success(f"🤖 Торговая система инициализирована: {registered_count}/{len(self.bot_configs)} ботов активно")

            # Запускаем мониторинг в фоне
            asyncio.create_task(self.bot_manager.start_monitoring())

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Критическая ошибка инициализации торговой системы: {str(e)}", e)
            return False

    async def process_trading_signal(self, signal: TradingSignal, source_channel: str) -> bool:
        """Обрабатывает торговый сигнал"""
        try:
            if not self.trading_enabled or not self.bot_manager:
                return False

            if self.logger:
                await self.logger.info(f"🎯 Обработка торгового сигнала от {source_channel}: {signal.direction} {signal.ticker}")

            # Передаем сигнал менеджеру ботов
            result = await self.bot_manager.process_signal(signal, source_channel)

            if result:
                if self.logger:
                    await self.logger.success(f"✅ Торговый сигнал от {source_channel} успешно обработан")
            else:
                if self.logger:
                    await self.logger.info(f"ℹ️ Торговый сигнал от {source_channel} не обработан (нет активного бота или бот отключен)")

            return result

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка обработки торгового сигнала от {source_channel}: {str(e)}", e)
            return False

    async def get_trading_stats(self) -> Dict:
        """Возвращает статистику торговли"""
        try:
            if not self.bot_manager:
                return {"trading_enabled": False, "error": "Менеджер ботов не инициализирован"}

            stats = await self.bot_manager.get_bot_stats()
            stats["trading_enabled"] = self.trading_enabled
            stats["testnet"] = self.testnet

            return stats

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка получения статистики торговли: {str(e)}", e)
            return {"trading_enabled": self.trading_enabled, "error": str(e)}

    async def disable_trading_for_channel(self, channel: str) -> bool:
        """Отключает торговлю для канала"""
        try:
            if not self.bot_manager:
                return False

            result = await self.bot_manager.disable_bot(channel)

            if result and self.logger:
                await self.logger.info(f"🚫 Торговля отключена для канала: {channel}")

            return result

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка отключения торговли для {channel}: {str(e)}", e)
            return False

    async def enable_trading_for_channel(self, channel: str) -> bool:
        """Включает торговлю для канала"""
        try:
            if not self.bot_manager:
                return False

            result = await self.bot_manager.enable_bot(channel)

            if result and self.logger:
                await self.logger.info(f"✅ Торговля включена для канала: {channel}")

            return result

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Ошибка включения торговли для {channel}: {str(e)}", e)
            return False

    def is_trading_enabled(self) -> bool:
        """Проверяет, включена ли торговля"""
        return self.trading_enabled and self.bot_manager is not None

    def has_bot_for_channel(self, channel: str) -> bool:
        """Проверяет, есть ли бот для канала"""
        return channel in self.bot_configs 
