# Новые функции торгового бота

## 🔄 Автоматическое перемещение стоп-лосса в безубыток

### Описание
После исполнения первого тейк-профита бот автоматически перемещает стоп-лосс на цену входа, обеспечивая безубыточность сделки.

### Как работает
1. **Мониторинг тейк-профитов**: Бот постоянно проверяет статус всех тейк-профитов
2. **Условие активации**: Когда исполняется первый тейк-профит
3. **Перемещение стопа**: Стоп-лосс автоматически перемещается на 1 тик от цены входа в нужную сторону
4. **Обновление на бирже**: Старый стоп-ордер отменяется, создается новый

### Преимущества
- ✅ **Защита капитала**: Минимально возможный риск (1 тик) после первой прибыли
- ✅ **Автоматизация**: Не требует ручного вмешательства
- ✅ **Психологический комфорт**: Снижение стресса от торговли
- ✅ **Совместимость с биржей**: Стоп-лосс не отклоняется из-за некорректной цены
- ✅ **Точность**: Использует реальный минимальный шаг цены для каждой торговой пары

### Пример
```
Сделка: LONG BTC @ $50,000 (tick_size = 0.01)
Стоп-лосс: $48,000 (риск $2,000)
Тейк-профиты: $52,000, $54,000, $56,000

После исполнения первого TP @ $52,000:
✅ Стоп-лосс перемещен на $49,999.99 (-1 тик)
✅ Риск уменьшен: с $2,000 до $0.01
✅ Защищенная прибыль: $2,000 × частичный размер

Для микро-цен (SHIB @ $0.00001500, tick_size = 0.00000001):
✅ Стоп-лосс перемещен на $0.00001499 (-1 тик)
✅ Риск: минимально возможный для данной пары
```

---

## 📊 Режим grid_open - Сетка входа

### Описание
Вместо одного рыночного ордера бот размещает 10 лимитных ордеров с шагом 1%, обеспечивая лучшую цену входа и усреднение позиции.

### Как работает
1. **Разделение позиции**: Общий размер делится на 10 равных частей
2. **Расчет цен сетки**: 
   - **LONG**: Первый ордер по рыночной цене, остальные дешевле на 1% каждый
   - **SHORT**: Первый ордер по рыночной цене, остальные дороже на 1% каждый
3. **Размещение ордеров**: Все 10 лимитных ордеров размещаются одновременно
4. **Мониторинг исполнения**: Отслеживание каждого ордера отдельно

### Конфигурация

Обе функции настраиваются прямо в `SOURCE_CHANNELS` в файле `index_v3.py`:

```python
SOURCE_CHANNELS = {
    "Акулы рынка": {
        "id": -1002667675217,
        "forward_type": "custom",
        "signal_fn": "signal_analyzer",
        "signals_only": True,
        "max_volume": 100,
        "leverage": 20,
        "portfolio_percent": 3.0,
        "open_mode": "grid_open",  # Включает режим сетки
        "move_stop_to_breakeven": True  # Включает перемещение на безубыток
    },
    "GotBit Trades": {
        "id": -1002148391383,
        "forward_type": "custom",
        "signal_fn": "signal_analyzer",
        "signals_only": True,
        "max_volume": 50,
        "leverage": 15,
        "portfolio_percent": 0.5,
        "open_mode": "default",  # Обычный режим (по умолчанию)
        "move_stop_to_breakeven": False  # Отключает перемещение на безубыток
    }
}
```

**Возможные значения `open_mode`:**
- `"default"` - обычный рыночный ордер (по умолчанию)
- `"grid_open"` - сетка из 10 лимитных ордеров

**Настройка перемещения стоп-лосса (`move_stop_to_breakeven`):**
- `true` - после первого TP стоп-лосс автоматически перемещается на цену входа (по умолчанию)
- `false` - стоп-лосс остается на исходной позиции

### Пример сетки

#### LONG позиция (BTC @ $50,000)
```
Ордер  1: 0.1 BTC @ $50,000.00 ( 0%)  ← рыночная цена
Ордер  2: 0.1 BTC @ $49,500.00 (-1%)  ← лучше на 1%
Ордер  3: 0.1 BTC @ $49,000.00 (-2%)  ← лучше на 2%
Ордер  4: 0.1 BTC @ $48,500.00 (-3%)  ← лучше на 3%
...
Ордер 10: 0.1 BTC @ $45,500.00 (-9%)  ← лучше на 9%
```

#### SHORT позиция (ETH @ $3,000)
```
Ордер  1: 1.0 ETH @ $3,000.00 (+0%)  ← рыночная цена
Ордер  2: 1.0 ETH @ $3,030.00 (+1%)  ← лучше на 1%
Ордер  3: 1.0 ETH @ $3,060.00 (+2%)  ← лучше на 2%
Ордер  4: 1.0 ETH @ $3,090.00 (+3%)  ← лучше на 3%
...
Ордер 10: 1.0 ETH @ $3,270.00 (+9%)  ← лучше на 9%
```

### Преимущества
- ✅ **Лучшие цены**: Усреднение входа по более выгодным ценам
- ✅ **Снижение проскальзывания**: Лимитные ордера вместо рыночных
- ✅ **Гибкость**: Частичное исполнение при слабом движении
- ✅ **Оптимизация**: Максимальное использование волатильности

### Недостатки
- ⚠️ **Частичное исполнение**: Не все ордера могут исполниться
- ⚠️ **Время исполнения**: Медленнее чем рыночный ордер
- ⚠️ **Сложность**: Больше ордеров для мониторинга

---

## 🛠️ Технические детали

### Новые классы и enum'ы

```python
class OpenMode(str, Enum):
    DEFAULT = "default"      # Обычный рыночный ордер
    GRID_OPEN = "grid_open"  # Сетка лимитных ордеров

@dataclass
class TradeInstance:
    # ... существующие поля ...
    open_mode: OpenMode = OpenMode.DEFAULT
    grid_orders: List[TradeOrder] = field(default_factory=list)
    breakeven_moved: bool = False
```

### Новые функции

```python
async def _create_market_entry_order(trade, symbol, position_size):
    """Создает обычный рыночный ордер"""

async def _create_grid_orders(trade, symbol, total_position_size):
    """Создает сетку из 10 лимитных ордеров"""

async def _move_stop_to_breakeven(trade):
    """Перемещает стоп-лосс на цену входа"""

async def _check_trade_orders(trade):
    """Проверяет статус всех ордеров и активирует безубыток"""
```

### Обновленные функции

```python
async def execute_signal(..., open_mode: OpenMode = OpenMode.DEFAULT):
    """Добавлен параметр режима открытия"""

class BotConfig:
    """Добавлено поле open_mode"""
    open_mode: OpenMode = OpenMode.DEFAULT
```

---

## 📈 Результаты тестирования

### Тест перемещения стоп-лосса
```
📊 Сделка: Long BTCUSDT (tick_size = 0.01)
💰 Цена входа: $50,000.00
🛡️ Изначальный стоп: $48,000.00 (риск $2,000)
🎯 Тейк-профиты: $52,000.00, $54,000.00, $56,000.00

✅ Первый TP исполнен @ $52,000.00
🔄 Стоп-лосс перемещен: $48,000.00 → $49,999.99 (-1 тик)
✅ Почти-безубыток активирован
💰 Защищенная прибыль: $13.33
🛡️ Риск уменьшен: с $40.00 до $0.02 (1 тик)

📊 Микро-токен: Long SHIBUSDT (tick_size = 0.00000001)
💰 Цена входа: $0.00001500
✅ Стоп-лосс перемещен на: $0.00001499 (-1 тик)
🛡️ Риск: минимально возможный для пары
```

### Тест режима grid_open
```
🔸 DEFAULT режим:
   1 рыночный ордер @ $50,000 (market)
   ✅ Мгновенное исполнение
   ⚠️ Возможное проскальзывание

🔸 GRID_OPEN режим:
   10 лимитных ордеров: $50,000 до $45,500 (-9%)
   ✅ Лучшие цены исполнения
   ✅ Усреднение входа
   ⚠️ Частичное исполнение возможно
```

---

## 🚀 Использование

### 1. Настройка в конфигурации канала

```python
# В index_v3.py в SOURCE_CHANNELS:
"Акулы рынка": {
    "id": -1002667675217,
    "forward_type": "custom",
    "signal_fn": "signal_analyzer",
    "signals_only": True,
    "max_volume": 500,
    "leverage": 20,
    "portfolio_percent": 3.0,
    "open_mode": "grid_open",  # ← Режим открытия позиций
    "move_stop_to_breakeven": True  # ← Перемещение стоп-лосса на безубыток
}
```

### 2. Использование функции grid_open

```python
# Альтернативный способ через bot_function:
"Канал": {
    "bot_function": "grid_open",  # ← Использует GRID_OPEN режим
    # остальные настройки...
}
```

### 3. Мониторинг в логах

```
📊 Ордер сетки 1/10: 0.1000 @ $50000.00 (ID: 12345)
📊 Ордер сетки 2/10: 0.1000 @ $49500.00 (ID: 12346)
...
✅ Ордер сетки 3/10 исполнен @ $49000.00
🔄 Стоп лосс перемещен на безубыток: $50000.00
```

---

## ⚡ Быстрый старт

1. **Обновите конфигурацию канала** в `SOURCE_CHANNELS`:
   - Добавьте `"open_mode": "grid_open"` для режима сетки
   - Добавьте `"move_stop_to_breakeven": true/false` для управления безубытком
2. **Перезапустите бота** для применения изменений
3. **Отправьте тестовый сигнал** для проверки
4. **Наблюдайте логи** для контроля исполнения

Функции активируются автоматически и не требуют дополнительной настройки! 