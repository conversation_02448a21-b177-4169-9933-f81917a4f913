# Анализ изображений в торговых сигналах

## 🎯 Описание

Функция анализа изображений позволяет извлекать торговые сигналы из изображений (скриншотов графиков, табли<PERSON>, схем), которые часто содержат важную информацию о стоп-лоссах, тейк-профитах и других параметрах сделки, которые не указаны в текстовой части сообщения.

## ⚙️ Настройка

### 1. Добавление параметра в конфигурацию канала

```python
SOURCE_CHANNELS = {
    "Родион TO THE MOON": {
        "id": -1002206290082,
        "forward_type": "custom",
        "signal_fn": "signal_analyzer",
        "signals_only": true,
        "analyze_images": true  # ← Включить анализ изображений для извлечения торговых данных
    }
}
```

### 2. Переменные окружения

Использует тот же `OPENROUTER_API_KEY` что и анализатор сигналов:

```env
# Обязательно для работы анализатора изображений
OPENROUTER_API_KEY=your_api_key_here
```

## 🖼️ Поддерживаемые типы изображений

Анализатор может извлекать торговую информацию из:

| Тип изображения | Описание | Извлекаемые данные |
|-----------------|----------|-------------------|
| **Скриншоты графиков** | TradingView, биржевые графики | Уровни поддержки/сопротивления, тренды |
| **Таблицы сигналов** | Структурированные торговые сигналы | Entry, Stop Loss, Take Profit, Leverage |
| **Схемы позиций** | Визуальные представления сделок | Направление, объемы, риски |
| **Аналитические диаграммы** | Технический анализ в картинках | Паттерны, уровни, прогнозы |

## 🎯 Когда использовать analyze_images

### ✅ Рекомендуется для каналов с:
- **Графическими сигналами** - основная информация в изображениях
- **Таблицами данных** - стоп-лоссы и тейки в картинках
- **Схематичными сигналами** - визуальное представление сделок
- **Смешанным контентом** - текст + важные данные в изображениях

### ❌ Не рекомендуется для каналов с:
- **Только текстовыми сигналами** - вся информация в тексте
- **Декоративными изображениями** - картинки не несут торговой информации
- **Частыми сигналами** - избыточные расходы на анализ

## 🤖 Поддерживаемые модели

Анализ изображений использует модели с поддержкой vision:

| Модель | Vision Support | Цена Input | Приоритет |
|--------|----------------|------------|-----------|
| `openai/gpt-4o` | ✅ | $2.5/1M + $7.5/1M images | ⭐⭐⭐ |
| `openai/gpt-4o-mini` | ✅ | $0.15/1M + $0.45/1M images | ⭐⭐⭐ |
| `anthropic/claude-3.5-sonnet` | ✅ | $3/1M + $4.5/1M images | ⭐⭐ |
| `google/gemini-pro-1.5` | ✅ | $1.25/1M + $1.25/1M images | ⭐ |

## 📊 Примеры использования

### Канал "Родион TO THE MOON"
В этом канале торговые сигналы часто представлены в виде изображений с графиками, где:
- **Текстовая часть**: содержит общую информацию о направлении сделки
- **Изображение**: содержит точные уровни Stop Loss, Take Profit, entry points

```json
{
  "direction": "Long",
  "ticker": "BTC/USDT", 
  "entry_price": 67500,
  "stop_loss": 65200,      // ← Извлечено из изображения
  "take_profits": [68500, 69800, 71200],  // ← Извлечено из изображения
  "leverage": 10,
  "source": "image_analysis"
}
```

### Канал "Нестеров в рынке"
Специализируется на графических сигналах с детальными схемами позиций:
- **Изображения**: содержат полную торговую схему
- **Текст**: краткое описание или комментарии

## 🔧 Интеграция с анализатором сигналов

Параметр `analyze_images` работает совместно с `signal_fn: "signal_analyzer"`:

1. **Анализ текста**: Извлекаются базовые параметры сигнала
2. **Анализ изображений**: Дополняются недостающие данные (SL/TP)
3. **Объединение данных**: Создается полный торговый сигнал
4. **Валидация**: Проверка корректности всех параметров

## 📈 Логирование и отладка

При работе анализатора изображений в логах появляются сообщения:

```
🖼️ Анализ изображения для канала "Родион TO THE MOON"
📊 Извлечено из изображения: SL: 65200, TP: [68500, 69800, 71200]
✅ Торговый сигнал обогащен данными из изображения
```

### Диагностика проблем:

1. **Нет анализа изображений**: Проверьте `analyze_images: true` в конфигурации
2. **Ошибки API**: Убедитесь в наличии `OPENROUTER_API_KEY`
3. **Неподдерживаемый формат**: Проверьте формат изображения (JPG, PNG, WebP)
4. **Превышение лимитов**: Мониторьте расходы на OpenRouter

## 💡 Рекомендации по использованию

### Оптимизация расходов:
- Используйте только для каналов с важной графической информацией
- Предпочитайте `gpt-4o-mini` для экономии средств
- Мониторьте частоту анализа изображений

### Качество анализа:
- Лучше всего работает с четкими, контрастными изображениями
- Таблицы и схемы анализируются точнее графиков
- Текст на изображениях должен быть читаемым

## 🧪 Тестирование

Для тестирования функциональности используйте скрипт `test_image_analysis.py`:

```bash
# Установите API ключ
export OPENROUTER_API_KEY=your_api_key_here

# Поместите тестовое изображение в папку media/
# Запустите тест
python3 test_image_analysis.py
```

### Пример вывода успешного анализа:
```
🧪 Тестирование анализа торговых сигналов из изображений
============================================================
🔧 Инициализируем анализатор сигналов...
🖼️ Тестируем анализ изображения: ./media/test_trading_chart.jpg
✅ Анализ изображения успешен!
   Direction: Long
   Ticker: BSW/USDT
   Entry Price: 0.01795
   Stop Loss: 0.0162
   Take Profits: [0.0198, 0.0215, 0.0235]
   Leverage: 10
   Channel: Test Channel

📊 JSON представление:
{
  "direction": "Long",
  "ticker": "BSW/USDT",
  "entry_price": 0.01795,
  "leverage": 10,
  "take_profits": [0.0198, 0.0215, 0.0235],
  "stop_loss": 0.0162,
  "timestamp": "2025-01-16T20:30:45.123",
  "channel": "Test Channel"
}
```

## ✅ Статус реализации

### Реализованные функции:
- ✅ **Кодирование изображений** в base64 для отправки в LLM
- ✅ **Vision prompts** для анализа торговых графиков и таблиц
- ✅ **Мультимодальные запросы** к OpenAI GPT-4o, GPT-4o-mini, Claude 3.5
- ✅ **Интеграция с основной системой** через параметр `analyze_images`
- ✅ **Fallback механизм** на анализ текста при ошибках
- ✅ **Автоматическое удаление** временных файлов
- ✅ **Подробное логирование** процесса анализа

### Поддерживаемые модели с vision:
- `openai/gpt-4o` - Лучшая точность, ~$0.01 за изображение
- `openai/gpt-4o-mini` - Экономичная, ~$0.001 за изображение  
- `anthropic/claude-3.5-sonnet` - Отличная для сложных схем

## 🚀 Будущие улучшения

- [ ] Кэширование результатов анализа похожих изображений
- [ ] Поддержка анализа нескольких изображений в одном сообщении  
- [ ] Извлечение дополнительных параметров (Risk/Reward, время экспирации)
- [ ] Интеграция с OCR для улучшения распознавания текста
- [ ] Поддержка видеофайлов с торговыми сигналами 