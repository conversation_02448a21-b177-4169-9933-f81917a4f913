#!/usr/bin/env python3
"""
Скрипт для миграции каналов из source_channels.json в базу данных PostgreSQL
"""

import json
import os
import sys
from dotenv import load_dotenv
from db_connection import DatabaseConnection

load_dotenv()


def load_channels_from_json(file_path: str) -> dict:
    """
    Загружает каналы из JSON файла
    
    Args:
        file_path (str): Путь к JSON файлу
        
    Returns:
        dict: Словарь каналов
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data.get('channels', {})
    except FileNotFoundError:
        print(f"❌ Файл {file_path} не найден!")
        return {}
    except json.JSONDecodeError as e:
        print(f"❌ Ошибка парсинга JSON в {file_path}: {e}")
        return {}
    except Exception as e:
        print(f"❌ Ошибка загрузки конфигурации каналов: {e}")
        return {}


def migrate_channels_to_db(json_file_path: str, db_connection: DatabaseConnection):
    """
    Мигрирует каналы из JSON файла в базу данных
    
    Args:
        json_file_path (str): Путь к JSON файлу
        db_connection (DatabaseConnection): Подключение к базе данных
    """
    print(f"🔄 Загружаем каналы из {json_file_path}...")
    channels = load_channels_from_json(json_file_path)
    
    if not channels:
        print("❌ Не найдено каналов для миграции")
        return
    
    print(f"✅ Найдено {len(channels)} каналов для миграции")
    
    # Подключаемся к базе данных
    if not db_connection.connect():
        print("❌ Не удалось подключиться к базе данных")
        return
    
    try:
        with db_connection.connection.cursor() as cursor:
            # Проверяем существование таблицы channels
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'channels'
                );
            """)
            
            if not cursor.fetchone()[0]:
                print("❌ Таблица 'channels' не существует в базе данных")
                print("💡 Убедитесь, что Django миграции выполнены")
                return
            
            # Очищаем таблицу (опционально)
            cursor.execute("DELETE FROM channels")
            print("🗑️  Очищена таблица channels")
            
            # Вставляем каналы
            for channel_name, config in channels.items():
                try:
                    cursor.execute("""
                        INSERT INTO channels (
                            name, telegram_id, forward_type, signal_fn, signals_only,
                            leverage, portfolio_percent, open_mode, move_stop_to_breakeven,
                            allow_signals_without_sl_tp, max_profit_percent, review,
                            position_lifetime, target_chat_id, wins, fails, wins_ratio
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        channel_name,
                        str(config.get('id', '')),
                        config.get('forward_type', 'custom'),
                        config.get('signal_fn', 'signal_analyzer'),
                        config.get('signals_only', True),
                        config.get('leverage', 10),
                        config.get('portfolio_percent', 0.25),
                        config.get('open_mode', 'default'),
                        config.get('move_stop_to_breakeven', True),
                        config.get('allow_signals_without_sl_tp', True),
                        config.get('max_profit_percent', 10.0),
                        config.get('review', True),
                        config.get('position_lifetime', '1h'),
                        config.get('target_chat_id', -4984770976),
                        config.get('wins', 0),
                        config.get('fails', 0),
                        config.get('wins_ratio', 0.0)
                    ))
                    print(f"✅ Мигрирован канал: {channel_name}")
                    
                except Exception as e:
                    print(f"❌ Ошибка миграции канала {channel_name}: {e}")
            
            # Подтверждаем изменения
            db_connection.connection.commit()
            print(f"🎉 Успешно мигрировано {len(channels)} каналов в базу данных")
            
    except Exception as e:
        print(f"❌ Ошибка при миграции: {e}")
        db_connection.connection.rollback()
    finally:
        db_connection.disconnect()


def main():
    """Основная функция"""
    print("🚀 Запуск миграции каналов из JSON в PostgreSQL")
    
    # Путь к JSON файлу
    json_file_path = "source_channels.json"
    
    if not os.path.exists(json_file_path):
        print(f"❌ Файл {json_file_path} не найден!")
        print("💡 Убедитесь, что файл находится в текущей директории")
        sys.exit(1)
    
    # Создаем подключение к базе данных
    db_connection = DatabaseConnection()
    
    # Проверяем подключение
    if not db_connection.test_connection():
        print("❌ Не удалось подключиться к базе данных")
        print("💡 Проверьте настройки в .env файле:")
        print("   - POSTGRES_HOST")
        print("   - POSTGRES_PORT") 
        print("   - POSTGRES_USER")
        print("   - POSTGRES_PASSWORD")
        print("   - POSTGRES_DB")
        sys.exit(1)
    
    # Запрашиваем подтверждение
    print(f"📋 Найдено каналов в {json_file_path}: {len(load_channels_from_json(json_file_path))}")
    response = input("🤔 Продолжить миграцию? (y/N): ")
    
    if response.lower() not in ['y', 'yes']:
        print("❌ Миграция отменена")
        sys.exit(0)
    
    # Выполняем миграцию
    migrate_channels_to_db(json_file_path, db_connection)
    
    print("✅ Миграция завершена!")


if __name__ == "__main__":
    main() 