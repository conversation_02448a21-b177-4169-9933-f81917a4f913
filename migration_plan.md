# План миграции с db_connection.py на API

## Текущее состояние

### db_connection.py используется для:
1. **Загрузка конфигурации каналов** - `load_source_channels_from_db()`
2. **Обновление статистики** - `update_channel_statistics()`
3. **Прямое подключение к PostgreSQL**

### Файлы, использующие db_connection.py:
- `index_v3.py` - импортирует `load_source_channels_from_db`, `update_channel_statistics`
- `trading_bot.py` - использует `update_channel_statistics` и `db_connection.get_channel_by_name`
- `migrate_channels_to_db.py` - использует `DatabaseConnection` для миграции

## План миграции

### Этап 1: Создание API функций для статистики

Добавить в `api_integration.py`:

```python
async def update_channel_statistics(self, channel_name: str, wins: int = None, fails: int = None) -> bool:
    """Обновляет статистику канала через API."""
    
async def get_channel_statistics(self, channel_name: str) -> Optional[dict]:
    """Получает статистику канала через API."""
```

### Этап 2: Обновление index_v3.py

Заменить импорты:
```python
# Вместо:
# from db_connection import load_source_channels_from_db, update_channel_statistics

# Использовать:
# from api_integration import APIIntegration
# integration = APIIntegration()
```

### Этап 3: Обновление trading_bot.py

Заменить функции статистики:
```python
# Вместо:
# from db_connection import update_channel_statistics
# from db_connection import db_connection

# Использовать:
# from api_integration import APIIntegration
# integration = APIIntegration()
```

### Этап 4: Удаление db_connection.py

После полной миграции удалить:
- `db_connection.py`
- `migrate_channels_to_db.py` (если больше не нужен)

## Преимущества миграции

### ✅ Централизация
- Все операции через API
- Единая точка управления
- Консистентность данных

### ✅ Масштабируемость
- Легко добавлять новые поля
- API для внешних систем
- Возможность кэширования

### ✅ Мониторинг
- Логирование всех операций
- Метрики производительности
- Алерты при ошибках

### ✅ Безопасность
- Аутентификация через API
- Валидация данных
- Контроль доступа

## Риски и меры предосторожности

### ⚠️ Зависимость от API
- **Риск**: API недоступен
- **Решение**: Retry логика, fallback на локальные данные

### ⚠️ Производительность
- **Риск**: Медленные API вызовы
- **Решение**: Кэширование, batch операции

### ⚠️ Совместимость
- **Риск**: Изменения в API
- **Решение**: Версионирование API, обратная совместимость

## Следующие шаги

1. **Добавить функции статистики** в `api_integration.py`
2. **Протестировать** новые функции
3. **Обновить** `index_v3.py` и `trading_bot.py`
4. **Провести тестирование** в staging среде
5. **Удалить** `db_connection.py` после успешной миграции

## Временная совместимость

Во время миграции можно поддерживать оба подхода:
```python
try:
    # Новый API подход
    result = await api_integration.update_statistics(channel, wins, fails)
except Exception as e:
    # Fallback на старый подход
    result = db_connection.update_channel_statistics(channel, wins, fails)
``` 