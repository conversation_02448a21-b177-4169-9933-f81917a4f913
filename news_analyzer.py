import os
import json
import time
import asyncio
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum

from pydantic import BaseModel, Field, ConfigDict
from pydantic_ai import Agent, RunContext
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from dotenv import load_dotenv
import httpx

load_dotenv()


def create_proxy_client() -> Optional[httpx.AsyncClient]:
    """Создает HTTP клиент с поддержкой SOCKS5 прокси если настроен в .env"""
    use_socks5 = os.getenv('SOCKS5', 'False').lower() == 'true'

    if not use_socks5:
        print("🌐 SOCKS5 прокси отключен")
        return None

    socks5_ip = os.getenv('SOCKS5_ip')
    socks5_port = os.getenv('SOCKS5_port')
    socks5_user = os.getenv('SOCKS5_USER')
    socks5_pass = os.getenv('SOCKS5_PASS')

    if not socks5_ip or not socks5_port:
        print("⚠️ SOCKS5 включен в .env, но SOCKS5_ip или SOCKS5_port не указаны")
        return None

    try:
        # Формируем URL прокси
        if socks5_user and socks5_pass:
            proxy_url = f"socks5://{socks5_user}:{socks5_pass}@{socks5_ip}:{socks5_port}"
            print(f"🔌 Настраиваем SOCKS5 прокси с авторизацией для OpenRouter: {socks5_ip}:{socks5_port}")
        else:
            proxy_url = f"socks5://{socks5_ip}:{socks5_port}"
            print(f"🔌 Настраиваем SOCKS5 прокси для OpenRouter: {socks5_ip}:{socks5_port}")

        # Создаем HTTP клиент с прокси
        client = httpx.AsyncClient(
            proxy=proxy_url,
            timeout=30.0,
            limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)
        )

        print("✅ SOCKS5 прокси клиент успешно создан")
        return client

    except Exception as e:
        print(f"❌ Ошибка при настройке SOCKS5 прокси для OpenRouter: {e}")
        return None


class NewsAnalysis(BaseModel):
    """Результат анализа новости с использованием Pydantic"""
    model_config = ConfigDict(
        validate_assignment=True,
        str_strip_whitespace=True,
        extra='forbid'
    )

    tickers: Optional[List[str]] = Field(
        default=None,
        description="Список тикеров финансовых активов (BTC, ETH, AAPL и т.д.)"
    )
    tags: Optional[List[str]] = Field(
        default=None,
        description="Релевантные теги (cryptocurrency, stocks, earnings, regulation и т.д.)"
    )
    sentiment: Optional[float] = Field(
        default=None,
        ge=-1.0,
        le=1.0,
        description="Численная оценка настроения от -1.0 (очень негативное) до 1.0 (очень позитивное)"
    )
    analysis_model: str = Field(..., description="Модель, использованная для анализа")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class ReviewAnalysis(BaseModel):
    """Результат анализа сообщения на предмет рыночного обзора"""
    model_config = ConfigDict(
        validate_assignment=True,
        str_strip_whitespace=True,
        extra='forbid'
    )

    is_review: bool = Field(..., description="Является ли сообщение рыночным обзором")
    confidence: Optional[float] = Field(
        default=None,
        ge=0.0,
        le=1.0,
        description="Уверенность модели в определении (0.0-1.0)"
    )
    review_type: Optional[str] = Field(
        default=None,
        description="Тип обзора: technical_analysis, market_outlook, price_levels, daily_review и т.д."
    )
    analysis_model: str = Field(..., description="Модель, использованная для анализа")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


@dataclass
class AnalyzerDependencies:
    """Зависимости для анализатора новостей"""
    api_key: str
    model_manager: 'ModelManager'
    logger: Optional[Any] = None


class ModelStatus(Enum):
    """Статус модели"""
    AVAILABLE = "available"
    BLOCKED = "blocked"
    UNKNOWN = "unknown"


class ModelManager(BaseModel):
    """Менеджер для управления доступными моделями с использованием Pydantic"""
    model_config = ConfigDict(arbitrary_types_allowed=True)

    # Резервный список моделей на случай, если не удастся получить список с API
    _fallback_models: List[str] = [
        # Meta models - Llama 4 series
        "meta-llama/llama-4-maverick:free",
        "meta-llama/llama-4-scout:free",

        # Ultra large models (200B+)
        "nvidia/llama-3.1-nemotron-ultra-253b-v1:free",
        "qwen/qwen3-235b-a22b:free",

        # Very large models (70B-200B)
        "meta-llama/llama-3.1-405b:free",
        "nvidia/llama-3.3-nemotron-super-49b-v1:free",
        "meta-llama/llama-3.3-70b-instruct:free",
        "deepseek/deepseek-r1-distill-llama-70b:free",
        "shisa-ai/shisa-v2-llama3.3-70b:free",

        # Large models (32B-70B)
        "qwen/qwen-2.5-72b-instruct:free",
        "qwen/qwen2.5-vl-72b-instruct:free",
        "bytedance-research/ui-tars-72b:free",
        "featherless/qwerky-72b:free",
        "thudm/glm-4-32b:free",
        "thudm/glm-z1-32b:free",
        "qwen/qwen3-32b:free",
        "qwen/qwen3-30b-a3b:free",
        "qwen/qwq-32b:free",
        "qwen/qwq-32b-preview:free",
        "deepseek/deepseek-r1-distill-qwen-32b:free",
        "arliai/qwq-32b-arliai-rpr-v1:free",
        "qwen/qwen2.5-vl-32b-instruct:free",
        "open-r1/olympiccoder-32b:free",
        "qwen/qwen-2.5-coder-32b-instruct:free",

        # Medium-large models (14B-30B)
        "mistralai/mistral-small-3.1-24b-instruct:free",
        "mistralai/mistral-small-24b-instruct-2501:free",
        "cognitivecomputations/dolphin3.0-r1-mistral-24b:free",
        "cognitivecomputations/dolphin3.0-mistral-24b:free",
        "google/gemma-3-27b-it:free",
        "google/gemini-2.0-flash-exp:free",
        "rekaai/reka-flash-3:free",

        # Medium models (7B-14B)
        "qwen/qwen3-14b:free",
        "deepseek/deepseek-r1-distill-qwen-14b:free",
        "agentica-org/deepcoder-14b-preview:free",
        "moonshotai/moonlight-16b-a3b-instruct:free",
        "opengvlab/internvl3-14b:free",
        "google/gemma-3-12b-it:free",
        "meta-llama/llama-3.2-11b-vision-instruct:free",
        "thudm/glm-4-9b:free",
        "thudm/glm-z1-9b:free",
        "google/gemma-2-9b-it:free",
        "qwen/qwen3-8b:free",
        "meta-llama/llama-3.1-8b-instruct:free",
        "nousresearch/deephermes-3-llama-3-8b-preview:free",

        # Specialized models (various sizes)
        "deepseek/deepseek-r1:free",
        "microsoft/phi-4-reasoning-plus:free",
        "microsoft/phi-4-reasoning:free",
        "deepseek/deepseek-v3-base:free",
        "deepseek/deepseek-r1-zero:free",
        "deepseek/deepseek-prover-v2:free",
        "deepseek/deepseek-chat-v3-0324:free",
        "deepseek/deepseek-chat:free",
        "microsoft/mai-ds-r1:free",
        "tngtech/deepseek-r1t-chimera:free",
        "mistralai/mistral-nemo:free",

        # Multimodal and special models
        "google/learnlm-1.5-pro-experimental:free"
    ]

    preferred_models: List[str] = Field(default_factory=list)
    http_client: Optional[httpx.AsyncClient] = Field(default=None)
    last_models_update: float = Field(default=0.0)
    models_update_interval: int = Field(default=3600)  # 1 час в секундах

    blocked_models: Dict[str, float] = Field(default_factory=dict)
    last_check_time: float = Field(default=0.0)
    check_interval: int = Field(default=3600)  # 1 час в секундах
    storage_file: str = Field(default="blocked_models.json")

    def model_post_init(self, __context: Any) -> None:
        """Загружает заблокированные модели после инициализации и инициализирует список моделей"""
        self._load_blocked_models()
        # Инициализируем список моделей из резервного списка (будет обновлен при первом запросе)
        self.preferred_models = self._fallback_models.copy()
        # HTTP клиент будет инициализирован при первом запросе к API

    def _load_blocked_models(self) -> None:
        """Загрузка списка заблокированных моделей из файла"""
        try:
            if os.path.exists(self.storage_file):
                with open(self.storage_file, 'r') as f:
                    data = json.load(f)
                    self.blocked_models = data.get('blocked_models', {})
                    self.last_check_time = data.get('last_check_time', 0.0)
        except Exception as e:
            print(f"Ошибка при загрузке заблокированных моделей: {e}")

    def _save_blocked_models(self) -> None:
        """Сохранение списка заблокированных моделей в файл"""
        try:
            data = {
                'blocked_models': self.blocked_models,
                'last_check_time': self.last_check_time
            }
            with open(self.storage_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"Ошибка при сохранении заблокированных моделей: {e}")

    def should_check_blocked_models(self) -> bool:
        """Проверяет, нужно ли проверить заблокированные модели"""
        current_time = time.time()
        return current_time - self.last_check_time > self.check_interval

    async def fetch_free_models(self) -> bool:
        """Получает список бесплатных моделей с OpenRouter API"""
        api_key = os.getenv('OPENROUTER_API_KEY')
        if not api_key:
            print("❌ OPENROUTER_API_KEY не найден в переменных окружения")
            return False

        # Создаем HTTP клиент если он еще не создан
        if not self.http_client:
            self.http_client = create_proxy_client() or httpx.AsyncClient(
                timeout=30.0,
                limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)
            )

        try:
            # Запрашиваем список моделей с OpenRouter API
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }

            response = await self.http_client.get(
                "https://openrouter.ai/api/v1/models",
                headers=headers
            )

            if response.status_code != 200:
                print(f"❌ Ошибка при получении списка моделей: {response.status_code} {response.text}")
                return False

            models_data = response.json()

            # Фильтруем только бесплатные модели и собираем информацию о них
            free_models_info = []
            for model in models_data.get("data", []):
                model_id = model.get("id")
                if model_id and model.get("pricing", {}).get("prompt") == 0:
                    # Собираем информацию о модели для сортировки
                    context_length = model.get("context_length", 0)
                    created = model.get("created", 0)

                    # Определяем категорию модели по размеру (примерно)
                    category = 0  # По умолчанию - низкий приоритет

                    # Определяем категорию по названию модели
                    name_lower = model_id.lower()

                    # Топовые модели (высший приоритет)
                    if any(x in name_lower for x in ["llama-4", "llama-3.3", "llama-3.1", "qwen3", "gemini", "claude-3", "mistral-3", "nemotron-ultra"]):
                        category = 5
                    # Очень большие модели
                    elif any(x in name_lower for x in ["70b", "72b", "100b", "200b", "253b", "405b"]):
                        category = 4
                    # Большие модели
                    elif any(x in name_lower for x in ["32b", "30b", "34b", "40b"]):
                        category = 3
                    # Средние модели
                    elif any(x in name_lower for x in ["14b", "16b", "20b", "24b", "27b"]):
                        category = 2
                    # Малые модели
                    elif any(x in name_lower for x in ["7b", "8b", "9b", "11b", "12b"]):
                        category = 1

                    free_models_info.append({
                        "id": model_id,
                        "category": category,
                        "context_length": context_length,
                        "created": created
                    })

            if not free_models_info:
                print("⚠️ Не найдено бесплатных моделей в ответе API")
                return False

            # Сортируем модели по категории (убывание), дате создания (убывание) и длине контекста (убывание)
            free_models_info.sort(key=lambda x: (-x["category"], -x["created"], -x["context_length"]))

            # Преобразуем отсортированный список в формат для preferred_models
            free_models = [f"{model['id']}:free" for model in free_models_info]

            # Обновляем список предпочтительных моделей
            self.preferred_models = free_models
            self.last_models_update = time.time()

            print(f"✅ Получено и отсортировано {len(free_models)} бесплатных моделей с OpenRouter API")
            return True

        except Exception as e:
            print(f"❌ Ошибка при получении списка моделей: {e}")
            return False

    def should_update_models(self) -> bool:
        """Проверяет, нужно ли обновить список моделей"""
        current_time = time.time()
        return current_time - self.last_models_update > self.models_update_interval

    async def get_available_model(self) -> Optional[str]:
        """Возвращает первую доступную модель из списка"""
        self._load_blocked_models()

        current_time = time.time()

        # Если прошел час, пробуем разблокировать модели
        if self.should_check_blocked_models():
            models_to_unblock = []
            for model, blocked_time in self.blocked_models.items():
                if current_time - blocked_time > self.check_interval:
                    models_to_unblock.append(model)

            for model in models_to_unblock:
                del self.blocked_models[model]

            self.last_check_time = current_time
            self._save_blocked_models()

        # Проверяем, нужно ли обновить список моделей
        if self.should_update_models():
            # Пробуем получить актуальный список моделей с API
            success = await self.fetch_free_models()
            if not success and not self.preferred_models:
                # Если не удалось получить список с API и у нас нет моделей, используем резервный список
                self.preferred_models = self._fallback_models.copy()
                print("⚠️ Используем резервный список моделей")

        # Если список моделей пуст, используем резервный список
        if not self.preferred_models:
            self.preferred_models = self._fallback_models.copy()
            print("⚠️ Список моделей пуст, используем резервный список")

        # Возвращаем первую не заблокированную модель
        for model in self.preferred_models:
            if model not in self.blocked_models:
                return model

        return None

    def is_content_moderation_error(self, error_msg: str) -> bool:
        """Проверяет, является ли ошибка связанной с модерацией контента"""
        return ("flagged for" in error_msg.lower() and 
                any(reason in error_msg.lower() for reason in ["violence", "harassment", "hate"]))

    def mark_model_as_blocked(self, model_name: str) -> None:
        """Помечает модель как заблокированную"""
        self.blocked_models[model_name] = time.time()
        self._save_blocked_models()

    def get_model_status(self, model_name: str) -> ModelStatus:
        """Возвращает статус модели"""
        if model_name in self.blocked_models:
            return ModelStatus.BLOCKED
        elif model_name in self.preferred_models:
            return ModelStatus.AVAILABLE
        else:
            return ModelStatus.UNKNOWN


# Глобальные переменные для агентов (будут инициализированы позже)
news_analysis_agent = None
review_analysis_agent = None

def create_news_analysis_agent():
    """Создает агента для анализа новостей без модели по умолчанию"""
    global news_analysis_agent
    if news_analysis_agent is None:
        # Создаем простого агента без модели по умолчанию (модель будет передаваться при каждом вызове)
        news_analysis_agent = Agent[AnalyzerDependencies, str](
            deps_type=AnalyzerDependencies,
            system_prompt=(
                'You are a financial market analyst. Analyze the provided content and return ONLY a valid JSON object. '
                'Focus exclusively on financial market implications. Return ONLY the JSON analysis without any other text. '
                'All fields are optional - omit them if the content is not financially relevant. '
                'Example formats: '
                '{"tickers": ["BTC", "ETH"], "tags": ["cryptocurrency", "market"], "sentiment": 0.65} '
                'OR for non-financial content: {} '
                'Guidelines:'
                '1. TICKERS: Extract only actual financial ticker symbols (BTC, ETH, AAPL, TSLA, etc.). Omit if none found.'
                '2. TAGS: Use ONLY ENGLISH financial/market tags like: cryptocurrency, stocks, earnings, regulation, technology, market, trading, geopolitics, energy, commodities, etc. Omit if not relevant.'
                '3. SENTIMENT: Score from -1.0 (bearish for markets) to 1.0 (bullish for markets), based on financial impact. Omit if neutral.'
                'CRITICAL RULES: '
                '- Return ONLY a valid JSON object, no other text'
                '- All fields are optional - return {} for non-financial content'
                '- DO NOT include original content in your response'
                '- DO NOT add explanations or comments'
                '- Focus only on market-relevant financial implications'
                '- ALL TAGS MUST BE IN ENGLISH ONLY'
                '- Ignore non-financial aspects of the content'
            ),
        )

    return news_analysis_agent


def create_review_analysis_agent():
    """Создает агента для анализа рыночных обзоров"""
    global review_analysis_agent
    if review_analysis_agent is None:
        review_analysis_agent = Agent[AnalyzerDependencies, str](
            deps_type=AnalyzerDependencies,
            system_prompt=(
                'You are a market review classifier. Determine if the provided text is a market review/analysis and return ONLY a valid JSON object. '
                'A market review includes: technical analysis, price level discussions, market outlook, trend analysis, support/resistance levels, '
                'trading ranges, institutional activity analysis, daily/weekly reviews, price predictions, and market structure analysis. '
                'Return ONLY the JSON without any other text. '
                'Required format: {"is_review": true/false, "confidence": 0.0-1.0, "review_type": "type_if_applicable"} '
                'Review types: "technical_analysis", "market_outlook", "price_levels", "daily_review", "trend_analysis", "institutional_analysis" '
                'Examples of market reviews: '
                '- "399.85 – 459.00, где ранее фиксировалась прибыль институционалов..." (institutional_analysis) '
                '- "📈Ежедневный разбор📈 На дневном графике биткоин сформировал..." (daily_review) '
                '- "Уровни поддержки находятся на отметках 50000-51000..." (price_levels) '
                'NOT market reviews: trading signals, news, announcements, advertisements, personal messages '
                'CRITICAL RULES: '
                '- Return ONLY a valid JSON object'
                '- is_review field is required (true/false)'
                '- confidence should reflect your certainty'
                '- Analyze content in Russian and English'
            ),
        )

    return review_analysis_agent


class NewsAnalyzer:
    """Анализатор новостей с использованием PydanticAI"""

    def __init__(self, logger=None):
        self.api_key = os.getenv('OPENROUTER_API_KEY')
        if not self.api_key:
            raise ValueError("OPENROUTER_API_KEY не найден в переменных окружения")

        self.model_manager = ModelManager()
        self.logger = logger

        # Настройка санитизации текста из .env
        self.sanitize_text = os.getenv('SANITIZE_NEWS_TEXT', 'True').lower() == 'true'

        # Создаем HTTP клиент с прокси если настроен
        self.http_client = create_proxy_client()

        # Создаем агентов при инициализации анализатора
        self.agent = create_news_analysis_agent()
        self.review_agent = create_review_analysis_agent()

    async def close(self):
        """Закрывает HTTP клиент если он используется"""
        if self.http_client:
            await self.http_client.aclose()

    def _create_openrouter_model(self, model_name: str) -> OpenAIModel:
        """Создает модель OpenRouter для использования с PydanticAI"""
        provider = OpenAIProvider(
            api_key=self.api_key,
            base_url='https://openrouter.ai/api/v1',
            http_client=self.http_client  # Используем прокси-клиент если настроен
        )
        return OpenAIModel(model_name=model_name, provider=provider)

    def _sanitize_text_for_moderation(self, text: str) -> str:
        """Очищает текст от слов, которые могут вызвать срабатывание модерации"""
        # Заменяем проблемные слова на нейтральные
        sensitive_words = {
            # Русские слова
            'убийство': 'инцидент',
            'убил': 'причинил вред',
            'убили': 'причинили вред',
            'убиты': 'пострадали',
            'убит': 'пострадал',
            'война': 'конфликт',
            'военный': 'конфликтный',
            'взрыв': 'инцидент',
            'взорвал': 'инцидент произошел',
            'теракт': 'инцидент',
            'терроризм': 'экстремизм',
            'убийца': 'подозреваемый',
            'смерть': 'потеря',
            'погиб': 'пострадал',
            'погибли': 'пострадали',
            'умер': 'пострадал',
            'умерли': 'пострадали',
            'насилие': 'инцидент',
            'жестокость': 'серьезность',
            'атака': 'инцидент',
            'нападение': 'инцидент',
            'силой': 'мерами',
            'ответим силой': 'примем меры',
            'применение силы': 'принятие мер',
            'вооруженный': 'участвующий',
            'оружие': 'средства',
            'стрельба': 'инцидент',
            'выстрел': 'звук',
            'раненый': 'пострадавший',
            'ранен': 'пострадал',
            'кровь': 'ущерб',
            'жертва': 'пострадавший',
            'жертвы': 'пострадавшие',
            'пытки': 'давление',
            'избили': 'воздействовали',

            # Английские слова
            'kill': 'affect',
            'killed': 'affected',
            'murder': 'incident',
            'death': 'loss',
            'died': 'affected',
            'violence': 'incident',
            'attack': 'incident',
            'war': 'conflict',
            'weapon': 'tool',
            'shoot': 'incident',
            'blood': 'damage',
            'victim': 'affected person',
            'victims': 'affected people',
            'f*ck': 'problem',
            'fucked': 'problematic',
            'shit': 'problem',
            'damn': 'very',
            'hell': 'situation',
            'assault': 'incident',
            'rape': 'serious incident',
            'abuse': 'misuse',
            'torture': 'pressure'
        }

        sanitized_text = text
        for word, replacement in sensitive_words.items():
            # Заменяем с учетом регистра
            sanitized_text = sanitized_text.replace(word, replacement)
            sanitized_text = sanitized_text.replace(word.capitalize(), replacement.capitalize())
            sanitized_text = sanitized_text.replace(word.upper(), replacement.upper())

        return sanitized_text

    async def analyze_news(self, text: str) -> Optional[NewsAnalysis]:
        """Анализирует новость и возвращает результат"""
        if not text or len(text.strip()) < 10:
            return None

        # Ограничиваем длину текста для анализа
        text = text[:2000] if len(text) > 2000 else text

        # Пробуем модели по порядку приоритета
        max_attempts = 5
        attempt = 0

        while attempt < max_attempts:
            model = await self.model_manager.get_available_model()
            if not model:
                blocked_count = len(self.model_manager.blocked_models)
                total_count = len(self.model_manager.preferred_models)
                if self.logger:
                    await self.logger.error(f"Нет доступных моделей для анализа! Заблокировано: {blocked_count}/{total_count} моделей. Заблокированные: {list(self.model_manager.blocked_models.keys())[:5]}")
                return None

            if self.logger:
                blocked_count = len(self.model_manager.blocked_models) 
                await self.logger.info(f"Пробуем анализ с моделью: {model} (попытка {attempt + 1}/{max_attempts}, заблокировано моделей: {blocked_count})")

            try:
                # Создаем зависимости для агента
                deps = AnalyzerDependencies(
                    api_key=self.api_key,
                    model_manager=self.model_manager,
                    logger=self.logger
                )

                # Создаем модель для OpenRouter
                openrouter_model = self._create_openrouter_model(model)

                # Применяем санитизацию если она включена
                analysis_text = text
                if self.sanitize_text:
                    analysis_text = self._sanitize_text_for_moderation(text)
                    if self.logger and analysis_text != text:
                        await self.logger.info(f"Применена санитизация текста для модели {model}")

                # Запускаем анализ с агентом
                result = await self.agent.run(
                    f"Analyze and return JSON only: {analysis_text}",
                    deps=deps,
                    model=openrouter_model
                )

                if result.output:
                    # Парсим JSON из текстового ответа
                    try:
                        # Очищаем ответ от лишнего текста и извлекаем JSON
                        response_text = result.output.strip()

                        # Ищем JSON в ответе
                        json_start = response_text.find('{')
                        json_end = response_text.rfind('}') + 1

                        if json_start >= 0 and json_end > json_start:
                            json_str = response_text[json_start:json_end]
                            analysis_data = json.loads(json_str)

                            # Создаем объект NewsAnalysis из распарсенного JSON
                            analysis = NewsAnalysis(
                                tickers=analysis_data.get('tickers'),
                                tags=analysis_data.get('tags'),
                                sentiment=analysis_data.get('sentiment'),
                                analysis_model=model
                            )

                            return analysis
                        else:
                            # Если JSON не найден, пробуем парсить весь ответ
                            analysis_data = json.loads(response_text)
                            analysis = NewsAnalysis(
                                tickers=analysis_data.get('tickers'),
                                tags=analysis_data.get('tags'),
                                sentiment=analysis_data.get('sentiment'),
                                analysis_model=model
                            )
                            return analysis

                    except json.JSONDecodeError as je:
                        if self.logger:
                            await self.logger.warning(f"Модель {model} вернула неверный JSON формат: {je}. Ответ: {result.output[:200]}")
                        # Помечаем модель как заблокированную за неправильный формат ответа
                        self.model_manager.mark_model_as_blocked(model)

            except Exception as e:
                error_msg = str(e)
                should_block_model = True

                if self.logger:
                    # Различаем типы ошибок для более информативного логирования
                    if "rate limit" in error_msg.lower() or "quota" in error_msg.lower():
                        await self.logger.warning(f"Модель {model} заблокирована лимитами API: {error_msg}")
                    elif self.model_manager.is_content_moderation_error(error_msg):
                        await self.logger.warning(f"Модель {model} заблокирована модерацией контента Meta (новости содержат 'конфликтный' контент). Переходим к следующей модели.")
                        # Модель будет заблокирована обычным способом ниже
                    elif "model not found" in error_msg.lower() or "no endpoints found" in error_msg.lower():
                        await self.logger.warning(f"Модель {model} недоступна (404): {error_msg}")
                    elif "network" in error_msg.lower() or "connection" in error_msg.lower():
                        proxy_info = "с SOCKS5 прокси" if self.http_client else "без прокси"
                        await self.logger.warning(f"Сетевая ошибка при обращении к модели {model} ({proxy_info}): {error_msg}")
                    elif "timeout" in error_msg.lower():
                        await self.logger.warning(f"Таймаут при обращении к модели {model}: {error_msg}")
                    elif "proxy" in error_msg.lower() or "socks" in error_msg.lower():
                        await self.logger.error(f"Ошибка SOCKS5 прокси при обращении к модели {model}: {error_msg}", e)
                    elif "validation error" in error_msg.lower() and "datetime" in error_msg.lower():
                        await self.logger.warning(f"Модель {model} вернула некорректные данные (ошибка валидации datetime): {error_msg}")
                    else:
                        await self.logger.error(f"Неизвестная ошибка при анализе с моделью {model}: {error_msg}", e)

                # Если модель не сработала, помечаем её как заблокированную
                if should_block_model:
                    self.model_manager.mark_model_as_blocked(model)

            attempt += 1

            if attempt < max_attempts:
                await asyncio.sleep(1)  # Небольшая пауза между попытками

        if self.logger:
            await self.logger.error(f"Критическая ошибка: Не удалось проанализировать новость ни одной из {max_attempts} попыток. Доступных моделей: {len(self.model_manager.preferred_models)}")

        return None

    async def analyze_review(self, text: str) -> Optional[ReviewAnalysis]:
        """Анализирует сообщение на предмет рыночного обзора"""
        if not text or len(text.strip()) < 10:
            return None

        # Ограничиваем длину текста для анализа
        text = text[:1500] if len(text) > 1500 else text

        # Пробуем модели по порядку приоритета
        max_attempts = 3
        attempt = 0

        while attempt < max_attempts:
            model = await self.model_manager.get_available_model()
            if not model:
                if self.logger:
                    await self.logger.error(f"Нет доступных моделей для анализа обзора!")
                return None

            if self.logger:
                await self.logger.info(f"🔍 Анализируем обзор с моделью: {model} (попытка {attempt + 1}/{max_attempts})")

            try:
                # Создаем зависимости для агента
                deps = AnalyzerDependencies(
                    api_key=self.api_key,
                    model_manager=self.model_manager,
                    logger=self.logger
                )

                # Создаем модель для OpenRouter
                openrouter_model = self._create_openrouter_model(model)

                # Применяем санитизацию если она включена
                analysis_text = text
                if self.sanitize_text:
                    analysis_text = self._sanitize_text_for_moderation(text)

                # Запускаем анализ с агентом для обзоров
                result = await self.review_agent.run(
                    f"Classify if this is a market review: {analysis_text}",
                    deps=deps,
                    model=openrouter_model
                )

                if result.output:
                    # Парсим JSON из текстового ответа
                    try:
                        # Очищаем ответ от лишнего текста и извлекаем JSON
                        response_text = result.output.strip()

                        # Ищем JSON в ответе
                        json_start = response_text.find('{')
                        json_end = response_text.rfind('}') + 1

                        if json_start >= 0 and json_end > json_start:
                            json_str = response_text[json_start:json_end]
                            review_data = json.loads(json_str)

                            # Создаем объект ReviewAnalysis из распарсенного JSON
                            review = ReviewAnalysis(
                                is_review=review_data.get('is_review', False),
                                confidence=review_data.get('confidence'),
                                review_type=review_data.get('review_type'),
                                analysis_model=model
                            )

                            return review
                        else:
                            # Если JSON не найден, пробуем парсить весь ответ
                            review_data = json.loads(response_text)
                            review = ReviewAnalysis(
                                is_review=review_data.get('is_review', False),
                                confidence=review_data.get('confidence'),
                                review_type=review_data.get('review_type'),
                                analysis_model=model
                            )
                            return review

                    except json.JSONDecodeError as je:
                        if self.logger:
                            await self.logger.warning(f"Модель {model} вернула неверный JSON формат для анализа обзора: {je}. Ответ: {result.output[:200]}")
                        # Помечаем модель как заблокированную за неправильный формат ответа
                        self.model_manager.mark_model_as_blocked(model)

            except Exception as e:
                error_msg = str(e)
                if self.logger:
                    if "rate limit" in error_msg.lower() or "quota" in error_msg.lower():
                        await self.logger.warning(f"Модель {model} заблокирована лимитами API для анализа обзора: {error_msg}")
                    elif self.model_manager.is_content_moderation_error(error_msg):
                        await self.logger.warning(f"Модель {model} заблокирована модерацией контента для анализа обзора. Переходим к следующей модели.")
                    else:
                        await self.logger.error(f"Ошибка при анализе обзора с моделью {model}: {error_msg}", e)

                # Если модель не сработала, помечаем её как заблокированную
                self.model_manager.mark_model_as_blocked(model)

            attempt += 1

            if attempt < max_attempts:
                await asyncio.sleep(1)  # Небольшая пауза между попытками

        if self.logger:
            await self.logger.warning(f"Не удалось проанализировать обзор ни одной из {max_attempts} попыток")

        return None 
