# Исправление размера стоп-лосса при перемещении на безубыток

## Проблема

При перемещении стоп-лосса на безубыток после исполнения первого тейк-профита возникала ошибка:

```
❌ Ошибка перемещения стопа на безубыток: The order size must be less than the available amount of 5.86 USDT
```

### Причина ошибки

1. **Исходная позиция**: 10.0 USDT
2. **Первый TP исполнен**: -3.33 USDT (1/3 от позиции)
3. **Остаток позиции**: 6.67 USDT
4. **Проблема**: Стоп-лосс пытался создаться с исходным размером 10.0 USDT
5. **Результат**: Биржа отклоняла ордер, так как доступно только 6.67 USDT

## Решение

### 1. Получение актуального размера позиции

Добавлена проверка актуального размера позиции с биржи перед созданием нового стоп-лосса:

```python
# КРИТИЧНО: Получаем актуальный размер позиции с биржи
# После исполнения TP размер позиции уменьшился
is_position_open, actual_position_size = await self._verify_position_still_open(trade)

if not is_position_open:
    await self._log_to_file(trade.id, f"⚠️ Позиция уже закрыта на бирже - отменяем перемещение стоп-лосса")
    return

# Используем актуальный размер позиции для стоп-лосса
stop_loss_size = actual_position_size
```

### 2. Использование актуального размера в ордере

Стоп-лосс теперь создается с правильным размером:

```python
# Размещаем новый стоп ордер на бирже с актуальным размером позиции
order_result = self.exchange.create_order(
    trade.stop_loss_order.symbol,
    'STOP_MARKET',
    side,
    stop_loss_size,  # Используем актуальный размер позиции
    None,
    order_params
)
```

### 3. Обновление локальных данных

Размер стоп-лосса обновляется в локальных данных:

```python
# Обновляем информацию о стоп ордере
trade.stop_loss_order.exchange_order_id = order_result['id']
trade.stop_loss_order.price = breakeven_stop_price
trade.stop_loss_order.amount = stop_loss_size  # Обновляем размер стоп-лосса
trade.breakeven_moved = True
```

## Тестирование

Создан комплексный тест `test_stop_loss_size_fix.py` который проверяет:

### Тест 1: Нормальное перемещение с уменьшенным размером
- ✅ Исходный размер позиции: 10.0
- ✅ Размер после исполнения TP: 6.67
- ✅ Размер нового стоп-лосса: 6.67
- ✅ Стоп-лосс успешно перемещен на безубыток

### Тест 2: Позиция уже закрыта
- ✅ Позиция закрыта - перемещение стоп-лосса отменено
- ✅ Ордер не создается для закрытой позиции

### Тест 3: Обработка ошибок биржи
- ✅ Ошибка биржи обработана - стоп-лосс обновлен локально
- ✅ Система продолжает работать при сбоях биржи

## Логирование

Добавлено подробное логирование для отслеживания изменений размера:

```
📊 Актуальный размер позиции: 6.67 (было 10.0)
🔄 Стоп лосс перемещен на безубыток: $0.99000000 (1 тик ниже входа, размер=6.67, tick_size=0.01, ID: new_stop_456)
```

## Преимущества исправления

1. **Точность**: Стоп-лосс создается с правильным размером
2. **Надежность**: Избегает ошибок биржи из-за неправильного размера
3. **Безопасность**: Проверяет статус позиции перед действиями
4. **Отказоустойчивость**: Обрабатывает ошибки биржи
5. **Прозрачность**: Подробное логирование всех операций

## Применимость

Исправление применяется ко всем режимам торговли:
- ✅ DEFAULT режим (обычные рыночные ордера)
- ✅ GRID_OPEN режим (сетка лимитных ордеров)
- ✅ LIMIT_OPEN_1_LIMIT_EXIT_5 режим
- ✅ Любые будущие режимы торговли

## Совместимость

- ✅ Обратная совместимость сохранена
- ✅ Существующие сделки не затронуты
- ✅ Работает со всеми типами позиций (LONG/SHORT)
- ✅ Поддерживает все торговые пары

---

**Статус**: ✅ Исправлено и протестировано  
**Дата**: 2025-01-23  
**Версия**: 1.0  
**Тесты**: Все пройдены успешно
