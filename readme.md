## Create api key
go https://my.telegram.org/auth

# Create .env and fill:
```
API_ID = <YOUR_API_ID>
API_HASH = <YOUR_API_HASH>
PHONE = <YOUR_PHONE>
SESSION_NAME = <HOW_YOU_WANT_TO_NAME_SESSION_FILE>
OUTPUT_FILE = <HOW_YOU_WANT_TO_NAME_FILE_WITH_CHATS_LIST>
ADMIN_ID = <TELEGRAM_USER_ID_FOR_ERROR_NOTIFICATIONS>
LONG_CAPTION_HANDLING = "separate"
ADMIN_LOG_LEVEL = "ERROR"
OPENROUTER_API_KEY = <YOUR_OPENROUTER_API_KEY>
```

**ADMIN_ID** - Telegram ID пользователя, который будет получать уведомления об ошибках и логи работы скрипта. Чтобы узнать свой Telegram ID, напишите боту @userinfobot или @my_id_bot.

## 🎯 Анализатор торговых сигналов (НОВОЕ!)

Дополнительная функция для автоматического извлечения структурированных торговых сигналов из сообщений Telegram каналов в формате JSON.

## 💰 Управление размером позиций (НОВОЕ!)

Система автоматического расчета размера позиций на основе процента от текущего баланса USDT для каждого источника сигналов.

### 🚀 Возможности анализатора сигналов:
- **Извлечение параметров сигналов** - направление (Long/Short), тикер, плечо, цены входа/выхода
- **JSON формат** - структурированные данные для интеграции с торговыми ботами
- **Высокая точность** - использование платных AI моделей (GPT-4o-mini, Claude-3.5)
- **Экономичность** - ~$0.0001 за анализ одного сигнала

### 💰 Возможности управления позициями:
- **Автоматическое масштабирование** - размер позиций адаптируется к росту/падению портфеля
- **Гибкое управление рисками** - разные проценты для разных источников (1.5%-5%)
- **Защита от переторговли** - позиции не открываются при недостаточном балансе
- **Совместимость** - сохранена поддержка фиксированных объемов

### ⚙️ Настройка анализатора сигналов:
Добавьте `"signal_fn": "signal_analyzer"` к каналу в `SOURCE_CHANNELS`:
```python
SOURCE_CHANNELS = {
    "Акулы рынка": {
        "id": -1002667675217, 
        "forward_type": "custom", 
        "signal_fn": "signal_analyzer"  # ← Включить анализ сигналов
    }
}
```

### 🖼️ Настройка анализа изображений (НОВОЕ!):
Добавьте `"analyze_images": true` для извлечения торговых данных из изображений:
```python
SOURCE_CHANNELS = {
    "Родион TO THE MOON": {
        "id": -1002206290082,
        "forward_type": "custom", 
        "signal_fn": "signal_analyzer",
        "signals_only": true,
        "analyze_images": true  # ← Анализ торговых сигналов из изображений
    }
}
```

### ⚙️ Настройка управления позициями:
Добавьте `"portfolio_percent"` к каналу для расчета размера позиций от баланса:
```python
SOURCE_CHANNELS = {
    "Акулы рынка": {
        "id": -1002667675217, 
        "forward_type": "custom", 
        "signal_fn": "signal_analyzer",
        "signals_only": True,
        "max_volume": 500,              # Резервное значение
        "leverage": 20,
        "portfolio_percent": 5.0        # ← 5% от баланса USDT
    }
}
```

### 📊 Пример извлеченного JSON:
```json
{
  "direction": "Short",
  "ticker": "UNI/USDT",
  "leverage": 15,
  "entry_price": 7.110,
  "entry_price_now": 7.105,
  "take_profits": [7.003, 6.795, 6.469],
  "stop_loss": 7.356,
  "deposit_percentage": 2.0
}
```

### 💰 Автоматическое получение рыночных цен (НОВОЕ!)
Система автоматически получает актуальные рыночные цены с биржи BingX для точного выставления лимитных ордеров:

- **entry_price_now** - текущая рыночная цена, полученная с биржи
- **Для Long позиций** - используется bid цена (лучшая цена покупки)
- **Для Short позиций** - используется ask цена (лучшая цена продажи)
- **Автоматическое заполнение** - поле заполняется при создании каждого сигнала
- **Приоритет в ордерах** - лимитные ордеры используют `entry_price_now` для точного исполнения

### 🎯 Оптимизация Take Profit для публичных сигналов (НОВОЕ!)
Система автоматически оптимизирует уровни тейк-профитов для опережения других участников рынка:

- **Умная оптимизация** - уменьшение TP на 2 минимальных шага цены (tick size) каждой пары
- **Индивидуальный подход** - tick size получается из параметров конкретной торговой пары через CCXT
- **Для Long позиций** - уменьшаем TP (встаем раньше в очереди на продажу)
- **Для Short позиций** - увеличиваем TP (встаем раньше в очереди на покупку)
- **Автоматическое применение** - работает для всех торговых сигналов без дополнительной настройки

**📚 Подробная документация:** [SIGNAL_ANALYZER.md](SIGNAL_ANALYZER.md)

### ⚙️ Настройка анализа рыночных обзоров:
Добавьте `"review": true` к каналу для автоматического распознавания аналитических постов:
```python
SOURCE_CHANNELS = {
    "Аналитический канал": {
        "id": -1001234567890,
        "forward_type": "custom",
        "review": True  # ← Анализ обзоров рынка
    }
}
```

Функция автоматически распознает:
- 📊 Технический анализ графиков
- 📈 Ежедневные разборы рынка  
- 🎯 Обсуждение уровней поддержки/сопротивления
- 🏛️ Анализ институциональной активности
- 📋 Прогнозы и трендовый анализ

**📚 Подробная документация:** [REVIEW_ANALYSIS_FEATURE.md](REVIEW_ANALYSIS_FEATURE.md)

### ⏰ Время жизни позиций (НОВОЕ!)
Автоматическое закрытие позиций по истечении заданного времени для ограничения рыночной экспозиции:

```python
SOURCE_CHANNELS = {
    "Скальпинг канал": {
        "id": -1001234567890,
        "forward_type": "custom",
        "signal_fn": "signal_analyzer",
        "signals_only": True,
        "position_lifetime": "30m"  # ← Позиция закроется через 30 минут
    }
}
```

**Поддерживаемые форматы времени:**
- `"30s"` - 30 секунд
- `"15m"` - 15 минут  
- `"2h30m"` - 2 часа 30 минут
- `"1h30m10s"` - 1 час 30 минут 10 секунд
- `"0s"` - отключено (по умолчанию)

**Функции:**
- ⏰ **Автоматическое закрытие** - позиции закрываются рыночным ордером
- 🗑️ **Отмена ордеров** - автоматически отменяются все SL и TP ордера
- 📊 **Подробные логи** - информация о времени жизни в файлах сделок
- 📱 **Уведомления** - сообщения в Telegram о закрытии по таймауту

**📚 Подробная документация:** [POSITION_LIFETIME_FEATURE.md](POSITION_LIFETIME_FEATURE.md)

## Система логирования и уведомлений

Скрипт оснащен многоуровневой системой логирования:

### 📊 Логирование торговых операций (НОВОЕ!)
- **Уведомления в TARGET чат** - подробная информация о каждой торговой операции
- **Открытие сделок** - данные о позиции, балансе, установленных ордерах
- **Закрытие сделок** - результаты, PnL, обновленный баланс
- **Статус ордеров** - информация об успешности размещения SL и TP

### 🔧 Системное логирование
- **Выводит логи в консоль** - все события отображаются в терминале для локального мониторинга
- **Отправляет уведомления админу в Telegram** - критические ошибки, предупреждения и важные события автоматически отправляются указанному в `ADMIN_ID` пользователю
- **Обеспечивает устойчивость к ошибкам** - скрипт не завершается при возникновении ошибок, а логирует их и продолжает работу
- **Автоматический перезапуск** - при критических ошибках скрипт автоматически перезапускается через 5 минут

### ⚙️ Настройка уведомлений о торговле
Добавьте в .env файл:
```env
TARGET_CHAT_ID = -1234567890  # ID чата для торговых уведомлений
```

### 📱 Пример торгового уведомления:
```
🤖 ТОРГОВЫЙ БОТ: 📊 НОВАЯ СДЕЛКА ОТКРЫТА

ID сделки: trade-abc123
Канал: Акулы рынка
Сигнал: LONG BTCUSDT
Цена входа: $50000.0
Размер позиции: 0.025
Объем USD: $1250.00
Плечо: 20x

💰 БАЛАНС СЧЕТА
Свободный USDT: $25000.00
Использовано: 5.0% от портфеля

📋 УСТАНОВЛЕННЫЕ ОРДЕРА
🛡️ Стоп-лосс: ✅ Установлен @ $48000.0
💰 Тейк-профиты (3):
   TP1: ✅ @ $52000.0
   TP2: ✅ @ $54000.0
   TP3: ✅ @ $56000.0
```

### Типы уведомлений:
- **INFO** - информационные сообщения о ходе работы
- **SUCCESS** - успешное выполнение операций
- **WARNING** - предупреждения (например, превышение лимитов API)
- **ERROR** - ошибки с полным стеком вызовов

### Примеры уведомлений:
- Запуск и остановка скрипта
- Обработка каналов и количество найденных сообщений
- Успешная отправка сообщений
- Ошибки API Telegram
- Проблемы с файловой системой

### Тестирование системы логирования:
Чтобы убедиться, что уведомления работают корректно, после первого запуска скрипта вы должны получить в Telegram сообщение вида:
```
🤖 Telegram Parser Bot

Level: SUCCESS
Time: 2024-01-15 10:30:45
Message:
Клиент Telegram успешно запущен и готов к работе
```

Если уведомления не приходят, проверьте:
1. Правильность указанного `ADMIN_ID` в .env файле
2. Что бот может отправлять сообщения (начните диалог с самим собой)
3. Что интернет-соединение стабильно

## Анализ новостей с помощью ИИ

Скрипт теперь включает интеллектуальный анализ новостей с использованием **Pydantic AI** и **OpenRouter API**:

### Возможности анализа:
- **Извлечение тикеров** - автоматическое определение финансовых инструментов (BTC, ETH, AAPL, TSLA и т.д.)
- **Категоризация по тегам** - классификация новостей (cryptocurrency, stocks, earnings, regulation, technology)
- **Анализ настроения** - численная оценка от -1.0 (очень негативное) до +1.0 (очень позитивное)
- **Оценка уверенности** - уровень достоверности анализа от 0.0 до 1.0
- **Система фоллбэков** - использование 70+ бесплатных моделей с автоматическим переключением

### Настройка OpenRouter API:
1. Регистрируйтесь на [openrouter.ai](https://openrouter.ai/)
2. Получите API ключ в настройках аккаунта
3. Добавьте `OPENROUTER_API_KEY` в файл .env

### Поддержка SOCKS5 прокси (необязательно):
Если у вас ограничен доступ к OpenRouter API, настройте SOCKS5 прокси в .env:
```
SOCKS5=True
SOCKS5_ip=*************
SOCKS5_port=1080
SOCKS5_USER=username    # необязательно
SOCKS5_PASS=password    # необязательно
```

**Установка зависимостей для SOCKS5:**
```bash
pip install httpx[socks]
```

### Как работает анализ:
1. **Автоматический анализ** - каждое новое сообщение анализируется ИИ
2. **Результаты в сообщениях** - анализ добавляется к пересылаемым сообщениям
3. **Простая обработка** - каждая новость анализируется независимо

### Пример анализа:
```
🤖 AI Анализ новости:
📈 Тикеры: BTC, ETH
🏷️ Теги: cryptocurrency, market, regulation
💭 Настроение: 🟢 Позитивное (0.65)
🎯 Уверенность: 87.5%
🧠 Модель: llama-3.3-70b-instruct
```

### Система ротации моделей:
- **70+ бесплатных моделей** от Meta, Google, Microsoft, DeepSeek и других
- **Автоматическое переключение** при достижении лимитов
- **Блокировка на 1 час** моделей с ошибками
- **Приоритизация** от больших (200B+ параметров) к малым (<7B)

## Start
```
python3 -m venv ./venv
source ./venv/bin/activate

# Установка из requirements.txt (рекомендуется)
pip install -r requirements.txt

# Или установка пакетов вручную:
pip install telethon
pip install python-dotenv
pip install pydantic>=2.0.0
pip install pydantic-ai
pip install aiohttp
pip install httpx[socks]  # для поддержки SOCKS5 прокси

# Обычный запуск скрипта
python index_v3.py

# Запуск с обновлением счетчиков (рекомендуется после долгого простоя)
python index_v3.py --update-counters
```

### 🔄 Флаг --update-counters (НОВОЕ!)

Специальный флаг для обновления счетчиков last_message_id после долгого простоя сервера:

**Когда использовать:**
- После долгого отключения сервера (несколько часов/дней)
- Когда в каналах накопилось много неактуальных сообщений
- Перед важным запуском в продакшн

**Что делает флаг:**
- 🔄 **Обновляет счетчики** - устанавливает last_message_id на последние сообщения во всех каналах
- 🚫 **Пропускает обработку** - не выполняет накопившиеся сообщения и сигналы
- 📊 **Подробные логи** - показывает процесс обновления каждого канала
- ➡️ **Продолжает работу** - после обновления переходит к обычному мониторингу

**Пример использования:**
```bash
# Сервер простаивал 2 дня, в каналах накопилось много сообщений
python index_v3.py --update-counters

# Вывод:
# 🔄 Режим обновления счетчиков установлен
# ✅ Акулы рынка: обновлен на ID 1247
# ✅ Скальпинг канал: обновлен на ID 892
# 🎯 Обновление завершено: 8/8 каналов обновлено
# ✅ Обновление счетчиков завершено. Переход к обычной работе...
# INFO: Запускаем основную функцию.
# SUCCESS: Клиент Telegram успешно запущен и готов к работе
# ... программа продолжает мониторинг новых сообщений

# Больше НЕ нужно запускать отдельно:
# python index_v3.py
```

При первом запуске нужно будет указать номер телефона, код подтверждения и пароль. Далее авторизация будет проходить через сессию. Сессия появится в папке с проектом <yout_session_name>.session.

## Файловая структура проекта

### Основные файлы:
- `index_v3.py` - основной скрипт с интеграцией анализа новостей и торговых сигналов
- `news_analyzer.py` - модуль анализа новостей на базе Pydantic AI
- `signal_analyzer.py` - модуль извлечения торговых сигналов с помощью AI
- `requirements.txt` - зависимости проекта
- `env.example` - пример конфигурации
- `QUICK_START.md` - краткая инструкция по запуску
- `SIGNAL_ANALYZER.md` - документация по анализатору сигналов
- `test_signal_analyzer.py` - тестовый скрипт для проверки анализатора сигналов
- `test_review_analyzer.py` - тестовый скрипт для проверки анализатора обзоров

### Генерируемые файлы:
- `blocked_models.json` - отслеживание моделей с превышением лимитов
- `last_message_*.json` - файлы с ID последних обработанных сообщений
- Файлы сессий `.session` - авторизация Telegram

### Документация задач:
- `tasks/entry_price_now_feature.md` - документация по функциональности получения рыночных цен
- `tasks/take_profit_optimization.md` - документация по оптимизации take profit уровней

# Lib docs
- [Метод для получения списка чатов](https://docs.telethon.dev/en/stable/modules/client.html#telethon.client.dialogs.DialogMethods.get_dialogs)
- [Pydantic AI Documentation](https://ai.pydantic.dev/)
- [OpenRouter API Documentation](https://openrouter.ai/docs)


