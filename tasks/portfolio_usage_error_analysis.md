# Анализ ошибки превышения лимита портфеля

## Проблема
Пользователь сообщил о том, что была открыта позиция SOL/USDT на $1164, несмотря на то, что система показала превышение лимита MAX_PORTFOLIO_USAGE (30%).

## Исследование

### 1. Анализ логов
Найдены две отдельные сделки SOL/USDT от канала "Crypto Aman🔐CryptoVipTools":
- Первая сделка: 3.0 SOL (около $588)
- Вторая сделка: 3.0 SOL (около $588)
- Общая сумма: ~$1164

### 2. Выявленная ошибка в коде
**КРИТИЧЕСКАЯ ОШИБКА**: В `_calculate_total_portfolio_usage` функция вызывалась с параметром `excluding_trade_id=trade_id`, что приводило к исключению текущей сделки из расчета общего использования портфеля.

### 3. Исправления
1. **Убрано исключение текущей сделки** из расчета `total_portfolio_percent`
2. **Добавлена дополнительная проверка** после расчета размера позиции
3. **Реализована синхронизация позиций** с биржей для обнаружения позиций, открытых вручную

### 4. НОВАЯ ПРОБЛЕМА: Неправильное использование portfolio_percent

**Выявлено**: Система использует фиксированный объем $10 вместо процента от портфеля.

**Причина**: `portfolio_percent` передается как `None` или `0` в функцию `_calculate_position_size`.

**Настройки в source_channels.json**:
```json
"Crypto Aman🔐CryptoVipTools": {
  "portfolio_percent": 0.25,  // 0.25% от портфеля
  "leverage": 10
}
```

**Ожидаемый расчет**:
- Общий баланс: $349.96
- 0.25% от портфеля: $349.96 × 0.0025 = $0.87
- С плечом 10x: $0.87 × 10 = $8.70
- Размер позиции: $8.70 ÷ $196.0 = 0.044 SOL

**Фактический результат**: 3.0 SOL (около $588)

### 5. Добавлена отладка
Добавлены логи для отслеживания значений `portfolio_percent` и `max_volume_usd` в:
- `execute_signal` (строка 766)
- `_calculate_position_size` (строка 195)

## Следующие шаги
1. Перезапустить систему с исправлениями
2. Проверить логи отладки для выявления причины передачи `portfolio_percent=None`
3. Исправить загрузку настроек из `source_channels.json`

## Статус
- ✅ Исправлена ошибка с исключением текущей сделки из расчета
- ✅ Добавлена дополнительная проверка лимитов
- ✅ Реализована синхронизация позиций
- 🔍 В процессе: Отладка передачи portfolio_percent 