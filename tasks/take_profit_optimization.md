# Задача: Оптимизация Take Profit уровней для публичных сигналов

## Описание задачи
Для торговли по публичным сигналам необходимо уменьшать take profit уровни на 2 пункта (0.0002) перед выставлением ордеров. Это позволяет исполнять ордеры раньше других трейдеров, следующих тем же сигналам.

## Логика оптимизации
- **Получаем tick size** из параметров торговой пары через CCXT
- **Для Long позиций**: уменьшаем TP на 2 * tick_size (встаем раньше в очереди на продажу)
- **Для Short позиций**: увеличиваем TP на 2 * tick_size (встаем раньше в очереди на покупку)
- **Применяется только к сигналам с заданными TP уровнями**

## Пример:
```
Для пары BTC/USDT (tick_size = 0.01):
Исходный сигнал Long:
- TP1: 50000.00
- TP2: 51000.00
- TP3: 52000.00

Оптимизированные TP:
- TP1: 49999.98 (50000.00 - 2 * 0.01)
- TP2: 50999.98 (51000.00 - 2 * 0.01)
- TP3: 51999.98 (52000.00 - 2 * 0.01)

Для пары SHIB/USDT (tick_size = 0.00000001):
Исходный сигнал Long:
- TP1: 0.00001500
- TP2: 0.00001600

Оптимизированные TP:
- TP1: 0.00001498 (0.00001500 - 2 * 0.00000001)
- TP2: 0.00001598 (0.00001600 - 2 * 0.00000001)
```

## Статус реализации
- [x] Добавлен метод _get_tick_size для получения минимального шага цены
- [x] Добавлен метод _optimize_take_profit_levels для оптимизации TP уровней
- [x] Модифицирован метод _create_take_profits для применения оптимизации
- [x] Обновлена документация
- [x] Протестирована функциональность

## Детали реализации

### 1. Метод _get_tick_size
- Получает минимальный шаг цены (tick size) из параметров торговой пары через CCXT
- Использует precision.price или limits.price.min
- Fallback на 0.01 при ошибках

### 2. Метод _optimize_take_profit_levels
- Оптимизирует каждый TP уровень на 2 * tick_size
- Для Long: уменьшает TP (встаем раньше в очереди на продажу)
- Для Short: увеличивает TP (встаем раньше в очереди на покупку)
- Округляет результат до tick_size

### 3. Интеграция в _create_take_profits
- Автоматически применяется ко всем TP ордерам
- Логирует оптимизацию в файл сделки
- Сохраняет исходные уровни при ошибках

### 4. Тестирование
Создан тестовый скрипт `test_take_profit_optimization.py` для проверки функциональности:
- ✅ Тест Long сигнала (BTC/USDT): TP уменьшились на 0.02
- ✅ Тест Short сигнала (ETH/USDT): TP увеличились на 0.02
- ✅ Тест с малым tick size (SHIB/USDT): корректная работа с микро-ценами
- ✅ Все тесты пройдены успешно

## Результат
Функциональность полностью реализована и протестирована. Теперь система автоматически оптимизирует все take profit уровни на 2 минимальных шага цены для каждой торговой пары, что позволяет опережать других участников рынка при торговле по публичным сигналам. 