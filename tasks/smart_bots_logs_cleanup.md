# Умная очистка логов торговых ботов

## Задача
Реализовать умную очистку логов ботов, которая:
1. Получает все позиции с биржи
2. Удаляет только те логи, для которых нет открытых позиций
3. Создает логи для позиций без логов (чтобы система могла их мониторить)

## Анализ текущей системы

### Структура логов ботов
- Файлы находятся в папке `./bots/`
- Формат имени: `YYYY-MM-DD--HH-MM__trade_id-log.md`
- Пример: `2025-08-05--13-04__c4e51ac0-log.md`
- В логе содержится символ торговой пары (например: `BRETT/USDT`)

### Получение позиций с биржи
- Функция `_get_open_positions_from_exchange()` в `trading_bot.py` (строка 1889)
- Возвращает список открытых позиций с полями: `symbol`, `side`, `contracts`, `size`, `notional`

### Текущая очистка логов
- Функция `clear_bots_logs_sync()` в `index_v3.py` (строка 423)
- Удаляет ВСЕ файлы в папке `./bots/`

## План реализации

### Шаг 1: Создать функцию получения позиций с биржи
- Создать универсальную функцию для получения всех открытых позиций
- Извлечь символы торговых пар

### Шаг 2: Создать функцию парсинга логов
- Парсить существующие логи для извлечения символов торговых пар
- Создать mapping: файл лога -> символ торговой пары

### Шаг 3: Реализовать умную очистку
- Сравнить символы из логов с символами открытых позиций
- Удалить логи, для которых нет открытых позиций
- Создать логи для позиций без соответствующих логов

### Шаг 4: Модифицировать `clear_bots_logs_sync()`
- Заменить простое удаление всех файлов на умную очистку

## Статус
- [x] Анализ структуры логов и позиций
- [x] Создание функции получения позиций с биржи
- [x] Создание функции парсинга логов
- [x] Реализация умной очистки
- [x] Модификация существующей функции очистки
- [x] Улучшение функции создания логов с детальной информацией о позициях
- [x] Тестирование

## Результаты тестирования

### Тест 1: Создание логов для позиций
✅ **ПРОЙДЕН** - Функция `create_log_for_position()` успешно создает логи с детальной информацией:
- Символ торговой пары
- Направление позиции (LONG/SHORT) 
- Размер позиции
- Цена входа и текущая цена
- Нереализованный PnL
- Биржевой символ

### Тест 2: Парсинг символов из логов
✅ **ПРОЙДЕН** - Функция `parse_symbol_from_log()` корректно извлекает символы:
- BTC из "**Сигнал:** SignalDirection.LONG BTC/USDT @ 45000"
- ETH из "**Сигнал:** SignalDirection.SHORT ETH/USDT @ 3000"  
- DOGE из "**Сигнал:** SignalDirection.LONG DOGE/USDT @ 0.08"

### Тест 3: Умная очистка логов
✅ **ПРОЙДЕН** - Функция `clear_bots_logs_sync()` работает как задумано:
- Нашла 3 тестовых лога (BTC, ETH, DOGE)
- Проверила отсутствие соответствующих позиций на бирже  
- Удалила все 3 лога без позиций
- Не создала новых логов (нет позиций без логов)
- Показала детальную статистику операции

### Тест 4: Получение позиций с биржи
✅ **ПРОЙДЕН** - Функция `get_open_positions_sync()` подключается к бирже:
- Загружает конфигурации торговых ботов (78 каналов)
- Возвращает пустой список при отсутствии позиций
- Обрабатывает ошибки подключения gracefully

## Реализованные функции

### 1. `parse_symbol_from_log(log_file_path: str) -> str`
- Извлекает символ торговой пары из файла лога
- Использует регулярное выражение для поиска паттерна "**Сигнал:** ... SYMBOL/USDT"
- Нормализует символ для сопоставления с биржей

### 2. `get_open_positions_sync() -> tuple[set, dict]`
- Получает все открытые позиции с биржи синхронно
- Возвращает множество символов и детальную информацию о позициях
- Включает: side, size, entry_price, mark_price, unrealized_pnl, original_symbol

### 3. `create_log_for_position(symbol: str, position_info: dict = None)`
- Создает лог для существующей позиции на бирже
- Использует детальную информацию о позиции из биржи
- Генерирует уникальный trade_id и timestamp
- Создает полный лог с информацией о PnL, ценах, размере позиции

### 4. `clear_bots_logs_sync()` (модифицирована)
- Умная очистка: удаляет только логи без соответствующих позиций
- Создает логи для позиций без соответствующих логов
- Выводит детальную статистику операции
- Безопасная работа: не удаляет все логи подряд

## Обнаруженные файлы и функции
- `trading_bot.py:1889` - `_get_open_positions_from_exchange()`
- `trading_bot.py:423` - `_log_to_file()` (создание логов)
- `trading_bot.py:141` - `_generate_log_filename()` (генерация имени лога)  
- `index_v3.py:423` - `clear_bots_logs_sync()` (текущая очистка)