# Уведомление о запуске парсера

## Задача
Добавить отправку сообщения в чат -4984770976 при старте скрипта index_v3.py с информацией о запуске парсера и количестве каналов в source_channels.json.

## Требования
1. Отправлять сообщение в чат -4984770976 при запуске парсера
2. Сообщение должно содержать:
   - Информацию о том, что парсер запущен
   - Количество каналов в списке source_channels.json
   - Дату и время запуска

## Структура файлов
- `index_v3.py` - основной скрипт парсера
- `source_channels.json` - файл с конфигурацией каналов (объект "channels")
- Чат -4984770976 используется как target_chat_id для многих каналов

## Реализация
1. ✅ Создана функция `count_channels_in_config()` для подсчета каналов
2. ✅ Создана функция `send_startup_notification()` для отправки уведомления  
3. ✅ Интегрирована отправка уведомления в процесс запуска в функции `main()`

## Детали реализации

### Функция `count_channels_in_config()`
- Подсчитывает количество каналов в source_channels.json
- Обрабатывает ошибки чтения файла и парсинга JSON
- Возвращает 0 при ошибках

### Функция `send_startup_notification()`  
- Отправляет сообщение в чат -4984770976
- Включает время запуска, количество каналов и статус готовности
- Использует существующий logger для отчетности

### Интеграция
- Вызывается после инициализации анализаторов в функции `main()`
- Выполняется до создания папки медиафайлов
- Не блокирует дальнейшую работу при ошибках

## Статус: ✅ ВЫПОЛНЕНО

Добавлен функционал отправки уведомления о запуске парсера в чат -4984770976 с информацией о количестве каналов. Код готов к тестированию.