# Добавление уведомлений о превышении MAX_PORTFOLIO_USAGE

## Задача
Добавить уведомления в TARGET телеграм канал, когда MAX_PORTFOLIO_USAGE не позволяет ставить ордер по сигналу.

## Анализ кода

### Текущая логика проверки MAX_PORTFOLIO_USAGE
В файле `trading_bot.py` (строки 580-588) есть проверка:
```python
# ПРОВЕРЯЕМ ЛИМИТ MAX_PORTFOLIO_USAGE С УЧЕТОМ УЖЕ ОТКРЫТЫХ ПОЗИЦИЙ
if total_portfolio_percent >= max_portfolio_usage:
    await self._log_to_file(trade_id, f"🚫 СИГНАЛ ОТКЛОНЕН: Превышен лимит использования портфеля ({total_portfolio_percent:.1f}% >= {max_portfolio_usage}%)")
    if self.logger:
        await self.logger.warning(f"🚫 Сигнал отклонен для {signal.ticker}: использование портфеля {total_portfolio_percent:.1f}% превышает лимит {max_portfolio_usage}%")
    return None
```

### Существующая система уведомлений
- Метод `_log_to_chat()` отправляет уведомления в TARGET_CHAT_ID
- Используется для уведомлений об открытии/закрытии сделок
- Проверено в коде: строка 361-365

## Решение
Добавить вызов `_log_to_chat()` в месте проверки MAX_PORTFOLIO_USAGE для отправки уведомления в TARGET телеграм канал.

## Реализация
1. Изменить код в `trading_bot.py` в методе `execute_signal()`
2. Добавить вызов `_log_to_chat()` после проверки превышения лимита
3. Включить информацию о сигнале, канале и текущем использовании портфеля

## Проверенные пути
- `trading_bot.py:580-588` - место проверки MAX_PORTFOLIO_USAGE ✓
- `trading_bot.py:361-365` - метод _log_to_chat() ✓
- Система уведомлений работает через TARGET_CHAT_ID ✓

## Статус
- [x] Анализ кода
- [x] Реализация
- [ ] Тестирование

## Что реализовано
Добавлено уведомление в TARGET телеграм канал при превышении MAX_PORTFOLIO_USAGE в файле `trading_bot.py` (строки 580-600).

### Формат уведомления:
```
🚫 **СИГНАЛ ОТКЛОНЕН - ПРЕВЫШЕН ЛИМИТ ПОРТФЕЛЯ**

**Канал:** Акулы рынка
**Сигнал:** LONG BTC/USDT
**Цена входа:** $50000.0

💰 **СОСТОЯНИЕ ПОРТФЕЛЯ**
**Общий баланс:** $10000.00
**Текущее использование:** 85.5%
**Лимит MAX_PORTFOLIO_USAGE:** 80%
**Активных позиций:** 3

⚠️ **Для размещения ордера требуется освободить портфель или увеличить лимит MAX_PORTFOLIO_USAGE в .env файле**
```

### Техническая реализация:
1. Добавлен код после проверки `if total_portfolio_percent >= max_portfolio_usage:`
2. Формируется подробное сообщение с информацией о сигнале и состоянии портфеля
3. Отправляется через существующий метод `_log_to_chat()` в TARGET_CHAT_ID
4. Включает практические советы по решению проблемы

## Готово к тестированию
Изменения внесены, функциональность готова к тестированию в рабочей среде. 