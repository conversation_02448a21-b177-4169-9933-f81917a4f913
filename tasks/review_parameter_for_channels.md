# Задача: Добавление параметра review для SOURCE_CHANNELS

## Цель
Добавить параметр `review` для каналов в SOURCE_CHANNELS, который будет активировать анализ LLM на предмет рыночных обзоров и автоматически репостить такие сообщения в целевой канал.

## Требования пользователя
1. ✅ Добавить параметр `review` (по умолчанию `false`)
2. ✅ При `review: true` - анализировать сообщения через LLM на предмет рыночных обзоров
3. ✅ Если сообщение определено как обзор рынка - репостить в target канал
4. ✅ Установить `review: true` для всех существующих SOURCE_CHANNELS

## Примеры обзоров от пользователя
```
399.85 – 459.00, где ранее фиксировалась прибыль институционалов и крупных участников рынка...
```

```
📈Ежедневный разбор📈 03.07.2025 | 💰
На дневном графике биткоин сформировал бычье поглощение...
```

## ✅ СТАТУС: ПОЛНОСТЬЮ РЕАЛИЗОВАНО

### Реализованная функциональность:

#### 1. Модель данных (news_analyzer.py)
- ✅ Создана модель `ReviewAnalysis` с полями:
  - `is_review: bool` - является ли сообщение обзором
  - `confidence: Optional[float]` - уверенность модели (0.0-1.0)
  - `review_type: Optional[str]` - тип обзора
  - `analysis_model: str` - использованная модель
  - `timestamp: str` - время анализа

#### 2. LLM Агент для анализа обзоров
- ✅ Создана функция `create_review_analysis_agent()` с системным промптом
- ✅ Агент распознает типы обзоров:
  - `technical_analysis` - технический анализ
  - `market_outlook` - прогноз рынка  
  - `price_levels` - обсуждение уровней цен
  - `daily_review` - ежедневные разборы
  - `trend_analysis` - анализ трендов
  - `institutional_analysis` - анализ институциональной активности

#### 3. Расширение NewsAnalyzer класса
- ✅ Добавлен `review_agent` в инициализацию
- ✅ Реализована функция `analyze_review(text: str)` 
- ✅ Обработка ошибок и блокировка моделей
- ✅ Санитизация текста для предотвращения блокировок

#### 4. Конфигурация каналов
- ✅ Функция `get_channel_config()` обновлена для поддержки параметра `review`
- ✅ Значение по умолчанию: `"review": False`
- ✅ Поддержка старого и нового формата конфигурации

#### 5. Интеграция в обработку сообщений (index_v3.py)
- ✅ Создана функция `analyze_review_if_needed()`
- ✅ Вызов анализа обзора в `send_message_to_target()`
- ✅ Логика: если `review: True` и сообщение является обзором → делается репост
- ✅ Логирование результатов с уверенностью и типами обзоров

#### 6. Настройка всех каналов
- ✅ Добавлен `"review": True` во все активные каналы SOURCE_CHANNELS
- ✅ Также обновлены закомментированные каналы для будущего использования

### Логика работы:
1. **Проверка параметра**: Если `review: false` → анализ пропускается
2. **LLM анализ**: Если `review: true` → текст отправляется на анализ
3. **Классификация**: LLM определяет является ли сообщение рыночным обзором
4. **Действие**: Если `is_review: true` → делается простой репост, иначе обычная обработка

### Примеры настройки:
```python
SOURCE_CHANNELS = {
    "Акулы рынка": {
        "id": -1002667675217, 
        "forward_type": "custom", 
        "signal_fn": "signal_analyzer", 
        "signals_only": True, 
        "review": True  # ← Анализ обзоров включен
    }
}
```

### Логи при работе:
```
📋 Начинаем анализ обзора для сообщения 12345 из Акулы рынка
📋 Обзор найден в Акулы рынка (ID: 12345): Тип: daily_review, Уверенность: 0.89, Модель: openai/gpt-4o-mini
📋 Сообщение 12345 из Акулы рынка определено как обзор - делаем репост
```

## 🎯 Результат
Функциональность полностью реализована и готова к использованию. Бот автоматически:
- Распознает рыночные обзоры и аналитику
- Отличает их от торговых сигналов и новостей  
- Делает простые репосты обзоров для сохранения оригинального форматирования
- Продолжает обычную обработку для остальных сообщений 