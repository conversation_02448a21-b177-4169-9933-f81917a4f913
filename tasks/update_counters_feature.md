# Флаг --update-counters: Обновление счетчиков last_message_id

## Описание задачи

Добавить флаг командной строки `--update-counters` для обновления счетчиков последних сообщений после долгого простоя сервера. Флаг обновляет счетчики последних сообщений (файлы `./last_messages/last_message_*.json`) без обработки накопившихся сообщений.

## Проблема

Когда сервер простаивает длительное время (несколько часов или дней), в каналах накапливается множество неактуальных сообщений и устаревших торговых сигналов. При запуске бота эти сообщения становятся неактуальными и их обработка может привести к:
- Открытию позиций по неактуальным ценам
- Потерям от устаревших торговых сигналов  
- Избыточной нагрузке на систему обработки

## Решение

### Реализованная функциональность:

1. **Парсинг аргументов командной строки**
   - Добавлен `argparse` для обработки флага `--update-counters`
   - Флаг имеет описание и справку

2. **Функция `update_all_last_message_ids()`**
   - Проходит по всем каналам из `SOURCE_CHANNELS`
   - Получает ID последнего сообщения в каждом канале
   - Обновляет соответствующий файл `last_message_*.json`
   - Логирует процесс для каждого канала

3. **Интеграция в run_client функцию**
   - Проверка флага в начале `run_client()` перед основным циклом
   - При активном флаге запускается только обновление счетчиков
   - Программа завершается после обновления

### Логика работы:

```python
# В функции run_client() ПЕРЕД основным циклом:
if args.update_counters:
    # 1. Подключение к Telegram
    await client.start(phone)
    
    # 2. Обновление всех счетчиков
    await update_all_last_message_ids()
    
    # 3. Логируем переход к обычной работе
    await logger.info("✅ Обновление счетчиков завершено. Переход к обычной работе...")

# Основной цикл запускается ВСЕГДА (и после обновления счетчиков, и при обычном запуске)
while True:
    await main()
```

## Исправления

### ❌ Проблема v1.0:
Изначально проверка флага была в `main()` функции, которая вызывается в цикле `run_client()`. Это приводило к повторному выполнению обновления счетчиков.

### ✅ Решение v1.1:
Перенесена проверка флага в `run_client()` **перед** основным циклом. Теперь:
- Флаг проверяется только один раз при запуске
- Обновление счетчиков происходит единожды  
- Программа корректно завершается без запуска основного цикла
- Основной цикл запускается только при обычном режиме

### ✅ Улучшение v1.2:
Переименован флаг с `--ignore-missed-signals` на `--update-counters`:
- Более точное описание действия
- Понятно техническим пользователям
- Короче и лаконичнее

### ✅ Улучшение v1.3:
Изменено поведение после обновления счетчиков:
- Программа НЕ завершается после обновления
- Продолжает работу в обычном режиме мониторинга
- Один запуск вместо двух команд
- Более удобное использование

## Примеры использования

### Сценарий 1: После долгого простоя
```bash
# Сервер простаивал 2 дня
python index_v3.py --update-counters

# Результат:
# 🔄 Режим обновления счетчиков установлен  
# ✅ Акулы рынка: обновлен на ID 1247
# ✅ Скальпинг канал: обновлен на ID 892
# 🎯 Обновление завершено: 8/8 каналов обновлено
# ✅ Обновление счетчиков завершено. Переход к обычной работе...
# INFO: Запускаем основную функцию.
# ... программа продолжает работу в обычном режиме
```

### Сценарий 2: Обычный запуск
```bash
# Обычный запуск без флага
python index_v3.py

# Результат:
# 🚀 Запуск Telegram Parser Bot...
# INFO: Запускаем основную функцию.
# ... программа работает в обычном режиме
```

## Структура файлов

### Обновляемые файлы:
- `./last_messages/last_message_Акулы_рынка.json`
- `./last_messages/last_message_Скальпинг_канал.json`
- `./last_messages/last_message_[Channel_Name].json`

### Формат файла:
```json
{
  "last_message_id": 1247
}
```

## Логирование

Система предоставляет подробные логи процесса:

- **INFO**: Начало процесса обновления
- **SUCCESS**: Успешное обновление каждого канала  
- **WARNING**: Пропуск каналов с ошибками конфигурации
- **ERROR**: Ошибки доступа к каналам
- **SUCCESS**: Итоговая статистика обновления

## Преимущества

1. **Безопасность**: Предотвращение торговли по устаревшим сигналам
2. **Эффективность**: Быстрое обновление без обработки старых сообщений
3. **Контроль**: Подробное логирование процесса
4. **Простота**: Один флаг для решения проблемы

## Совместимость

- ✅ Совместим с существующей системой `last_message_id`
- ✅ Не влияет на обычную работу бота
- ✅ Работает со всеми типами каналов (signal_analyzer, custom, repost)
- ✅ Поддерживает все настройки каналов

## Статус

✅ **ЗАВЕРШЕНО** - Функциональность полностью реализована и протестирована

### Внесенные изменения:

1. **index_v3.py**:
   - Добавлен импорт `argparse`
   - Добавлен парсер аргументов с флагом `--update-counters`
   - Реализована функция `update_all_last_message_ids()`
   - Интегрирована логика в `main()` функцию

2. **readme.md**:
   - Добавлена секция документации флага
   - Примеры использования
   - Описание сценариев применения

3. **tasks/update_counters_feature.md**:
   - Полная техническая документация
   - Примеры и сценарии использования 