# Исправление max_profit_percent поля и логики take profit'ов

## Проблема
Поле `max_profit_percent` работает неправильно. Нужно:
1. Переименовать `max_profit` в `max_take_profit_percent` 
2. Исправить логику: все take profit'ы выше цены из сигнала должны быть равны `max_take_profit_percent`

## Текущая реализация
- Поле называется `max_profit` (в TradeInstance и BotConfig)
- Логика в методе `_apply_max_profit_limit()` фильтрует TP уровни, но не устанавливает их равными max_profit

## Требуемые изменения

### 1. Переименование поля
- `TradeInstance.max_profit` → `TradeInstance.max_take_profit_percent`
- `BotConfig.max_profit` → `BotConfig.max_take_profit_percent`
- Параметр `max_profit` в методах → `max_take_profit_percent`

### 2. Исправление логики
В методе `_apply_max_profit_limit()`:
- Все TP уровни, которые превышают max_take_profit_percent, должны быть установлены равными этому значению
- Вместо фильтрации (удаления) превышающих TP, устанавливаем их в max_take_profit_percent

### 3. Пример логики
```python
# Для Long позиции с entry_price=100, max_take_profit_percent=5%
# Исходные TP: [102, 105, 110] 
# Результат: [102, 105, 105] (110 заменено на 105, т.к. 105 = 100 * 1.05)

# Для Short позиции с entry_price=100, max_take_profit_percent=5%  
# Исходные TP: [98, 95, 90]
# Результат: [98, 95, 95] (90 заменено на 95, т.к. 95 = 100 * 0.95)
```

## Файлы для изменения
1. `trading_bot.py` - основная логика
2. `bot_manager.py` - конфигурация бота
3. Все места использования поля `max_profit`

## Статус
- [x] Анализ текущей реализации
- [x] Переименование полей
- [x] Исправление логики take profit'ов
- [ ] Тестирование изменений

## Изменения выполнены

### 1. Переименование полей (✅ Выполнено)
- `TradeInstance.max_profit` → `TradeInstance.max_take_profit_percent`
- `BotConfig.max_profit` → `BotConfig.max_take_profit_percent`
- Все параметры методов обновлены

### 2. Исправление логики (✅ Выполнено)
В методе `_apply_max_profit_limit()` изменена логика:
- **Было**: Фильтрация (удаление) TP уровней, превышающих лимит
- **Стало**: Замена превышающих TP уровней на максимальное значение

**Новая логика для Long позиций:**
```python
# Заменяем тейки, которые превышают максимальную прибыль, на максимальное значение
limited_tp_levels = [min(tp, max_price) for tp in tp_levels]
```

**Новая логика для Short позиций:**
```python
# Заменяем тейки, которые превышают максимальную прибыль, на максимальное значение
limited_tp_levels = [max(tp, min_price) for tp in tp_levels]
```

### 3. Обновленные файлы
- `trading_bot.py` - основная логика и TradeInstance
- `bot_manager.py` - BotConfig и методы
- `trading_integration.py` - интеграция с конфигурацией
- `index_v3.py` - обработка конфигурации каналов

### 4. Совместимость с source_channels.json
Поле `max_profit_percent` в конфигурационных файлах уже использует правильное название, код обновлен для совместимости. 