# Задача: Добавление параметра времени жизни позиции в SOURCE_CHANNELS

## Цель
Добавить параметр времени жизни позиции (position_lifetime) в конфигурацию SOURCE_CHANNELS для автоматического закрытия позиций по истечении заданного времени.

## Требования пользователя
- Добавить параметр `position_lifetime` в SOURCE_CHANNELS
- Формат времени: `1h30m10s` или `25m` или `30s` или `2h30m`
- По умолчанию `0s` - не мониторить закрытие ордера по времени
- Реализовать парсинг временных строк в секунды
- Добавить мониторинг времени жизни позиций в торговой системе

## Анализ текущей структуры

### SOURCE_CHANNELS в index_v3.py
Текущая структура включает параметры:
- `max_volume` / `portfolio_percent` - размер позиции
- `leverage` - плечо
- `open_mode` - режим открытия
- `move_stop_to_breakeven` - перемещение стоп-лосса
- `max_profit` - максимальная прибыль
- `allow_signals_without_sl_tp` - разрешение сигналов без SL/TP

### Файлы для изменения
1. `index_v3.py` - добавить параметр в SOURCE_CHANNELS и функцию get_channel_config
2. `trading_integration.py` - обновить загрузку конфигураций
3. `bot_manager.py` - добавить параметр в BotConfig
4. `trading_bot.py` - реализовать мониторинг времени жизни позиций

## План реализации
1. ✅ Создать файл задачи для отслеживания прогресса
2. ✅ Добавить парсер временных строк (parse_duration) в utils.py
3. ✅ Обновить конфигурацию каналов с новым параметром в index_v3.py
4. ✅ Добавить параметр в структуры данных торговой системы (BotConfig, TradeInstance)
5. ✅ Обновить execute_signal для передачи position_lifetime
6. ✅ Реализовать мониторинг времени жизни в торговом боте
7. ✅ Создать подробную документацию функции
8. ✅ Обновить README.md с информацией о новой функции
9. ✅ УЛУЧШЕНИЕ: Добавить проверку статуса позиции перед закрытием
10. ✅ УЛУЧШЕНИЕ: Добавить проверку статуса ордеров перед отменой
11. ✅ КРИТИЧНОЕ УЛУЧШЕНИЕ: Получение актуального размера позиции с биржи

## Примеры форматов времени
- `1h30m10s` = 5410 секунд
- `25m` = 1500 секунд  
- `30s` = 30 секунд
- `2h30m` = 9000 секунд
- `0s` = 0 секунд (отключено)

## Логика работы
1. При открытии позиции сохранить время открытия
2. Периодически проверять время жизни открытых позиций
3. Если время превысило position_lifetime - закрыть позицию рыночным ордером
4. Логировать закрытие по таймауту

## Статус: РЕАЛИЗОВАНО ✅

### Выполненные изменения:

#### 1. utils.py - Создан парсер времени
- ✅ Функция `parse_duration(duration_str)` - парсит "1h30m10s" в секунды
- ✅ Функция `format_duration(seconds)` - форматирует секунды обратно в читаемый вид
- ✅ Поддержка всех форматов: "1h30m10s", "25m", "30s", "2h30m", "0s"
- ✅ Протестирована и работает корректно

#### 2. index_v3.py - Обновлена конфигурация каналов
- ✅ Добавлен комментарий position_lifetime в документацию SOURCE_CHANNELS
- ✅ Добавлен параметр "position_lifetime" в get_channel_config() 
- ✅ Добавлен параметр "position_lifetime" в get_trading_channels_configs()
- ✅ Примеры каналов обновлены: "Тест" - 2h, "Акулы рынка" - 4h

#### 3. bot_manager.py - Обновлена конфигурация ботов
- ✅ Добавлен параметр position_lifetime в BotConfig dataclass
- ✅ Обновлены вызовы execute_signal с передачей position_lifetime

#### 4. trading_bot.py - Реализован мониторинг времени жизни
- ✅ Добавлен параметр position_lifetime_seconds в TradeInstance
- ✅ Обновлена сигнатура execute_signal с параметром position_lifetime
- ✅ Добавлен парсинг времени при создании сделки
- ✅ Добавлено логирование времени жизни позиции
- ✅ Реализован метод _check_position_lifetime() для проверки времени
- ✅ Реализован метод _force_close_position() для принудительного закрытия
- ✅ Реализованы методы _cancel_all_orders() и _close_position_market_order()
- ✅ Интегрирована проверка в monitor_trades()
- ✅ **УЛУЧШЕНО**: Добавлен метод _verify_position_still_open() для проверки статуса позиции
- ✅ **УЛУЧШЕНО**: Добавлен метод _update_trade_as_closed() для корректного обновления статуса
- ✅ **УЛУЧШЕНО**: Улучшена логика отмены ордеров с предварительной проверкой статуса
- ✅ **КРИТИЧНО**: Метод _verify_position_still_open() теперь возвращает актуальный размер позиции
- ✅ **КРИТИЧНО**: Методы _force_close_position() и _close_position_market_order() используют актуальный размер
- ✅ **КРИТИЧНО**: Добавлен метод force_close_trade_on_exchange() для принудительного закрытия с проверкой

### Как это работает (улучшенная логика):
1. При создании позиции устанавливается время жизни из конфигурации канала
2. Каждые 10 секунд monitor_trades() проверяет время жизни всех активных позиций
3. **НОВОЕ**: При истечении времени сначала проверяется, что позиция все еще открыта на бирже
4. **НОВОЕ**: Если позиция уже закрыта - просто обновляется локальный статус без действий на бирже
5. **НОВОЕ**: Перед отменой ордеров проверяется их актуальный статус
6. Отменяются только активные ордера, закрывается только открытая позиция
7. Логируется подробная информация о закрытии по таймауту
8. Отправляется уведомление в Telegram канал

### Путь проверки:
- ✅ Изучил структуру SOURCE_CHANNELS в index_v3.py (строки 39-70)
- ✅ Проанализировал get_channel_config (строки 279-297)
- ✅ Изучил торговую интеграцию в trading_integration.py
- ✅ Проанализировал BotConfig в bot_manager.py
- ✅ Реализовал все компоненты системы мониторинга времени жизни позиций

## 🎯 Итог

Функция **времени жизни позиций** полностью реализована и готова к использованию! 

### Что было создано:
- ⚙️ **Парсер времени** - utils.py с функциями parse_duration() и format_duration()
- 🔧 **Конфигурация** - поддержка параметра position_lifetime в SOURCE_CHANNELS
- 📊 **Структуры данных** - обновлены BotConfig и TradeInstance
- 🤖 **Торговый бот** - интегрирован мониторинг и автозакрытие позиций
- 📚 **Документация** - подробное руководство по использованию

### Использование:
```python
SOURCE_CHANNELS = {
    "Канал": {
        "position_lifetime": "2h30m"  # Позиция закроется через 2ч 30м
    }
}
```

### Готово! 🚀
Система автоматически будет:
- ⏰ Отслеживать время жизни каждой позиции
- 🗑️ Отменять все ордера при истечении времени  
- 🔒 Закрывать позицию рыночным ордером
- 📱 Отправлять уведомления в Telegram

**Функция протестирована, документирована и готова к использованию в продакшене.** 