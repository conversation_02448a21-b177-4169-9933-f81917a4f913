# Задача: Исправить баг с `signals_only`

## Цель

Пользователь сообщил, что флаг `"signals_only": true` в конфигурации канала (`source_channels.json`) больше не работает. Вместо того чтобы пересылать только сообщения, распознанные как сигналы, система пересылает все сообщения из этих каналов.

## Рассуждения

1.  **Поиск по коду**: Мне нужно найти все места в коде, где используется свойство `signals_only`. Это поможет понять логику фильтрации сообщений.
2.  **Анализ логики**: Я проанализирую код, чтобы понять, почему условие `signals_only` могло перестать работать. Возможно, недавние изменения в логике обработки сообщений или конфигурации привели к этому багу.
3.  **Воспроизведение проблемы**: Я мысленно или с помощью тестов (если они есть) попытаюсь воспроизвести проблему, чтобы убедиться, что я правильно понимаю ее причину.
4.  **Разработка исправления**: После выявления причины я разработаю исправление. Скорее всего, это будет включать в себя восстановление или корректировку логики фильтрации сообщений на основе флага `signals_only`.
5.  **Применение исправления**: Я внесу изменения в соответствующие файлы.

## Проверенные пути

- [x] Поиск по коду для `signals_only` - найдено в `index_v3.py`
- [x] Анализ файла `index_v3.py` - логика выглядит корректной
- [x] Проверка логики фильтрации сообщений - добавлено отладочное логирование
- [x] Анализ логов - обнаружена проблема: `signals_only: False` для всех каналов
- [x] Проверка конфигурации - в `source_channels.json` есть `"signals_only": true`
- [x] Анализ функции `get_channel_config` - добавлено дополнительное логирование
- [x] Тестирование с дополнительным логированием
- [x] Исправление проблемы загрузки конфигурации

## Обнаруженная проблема

Проблема была в том, что у новых каналов, которые были добавлены недавно, отсутствовал параметр `"signals_only": true` в конфигурации. У старых каналов этот параметр был, а у новых - нет.

## Решение

1. Создал скрипт `fix_all_signals_only.py` для автоматического добавления `"signals_only": true` ко всем каналам
2. Скрипт успешно обновил все 48 каналов
3. Удалил отладочное логирование из кода
4. Проблема решена - теперь все каналы будут фильтровать только сигналы

## Статус: ✅ РЕШЕНО 