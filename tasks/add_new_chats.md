# Задача: Добавить новые чаты в `source_channels.json`

## Цель

Пользователь хочет добавить 32 новых телеграм-канала в файл `source_channels.json`. Для каждого канала необходимо указать стандартные настройки и, для некоторых, специальную настройку для парсинга изображений.

## Рассуждения

1.  **Прочитать существующие каналы**: Сначала я должен прочитать файл `source_channels.json`, чтобы получить список уже добавленных каналов. Это позволит избежать дубликатов.
2.  **Подготовить новые данные**: Я должен обработать предоставленный пользователем список новых каналов. Для каждого канала я найду соответствующий ID из большого списка, предоставленного пользователем.
3.  **Создать объекты каналов**: Для каждого нового канала я создам JSON-объект.
    *   `title`: Название канала, как указано пользователем.
    *   `id`: ID канала, извлеченный из предоставленного списка.
    *   `leverage`: 10
    *   `portfolio_percent`: 0.25
    *   `open_mode`: "default"
    *   `move_stop_to_breakeven`: true
    *   `review`: true
    *   `position_lifetime`: "15m"
    *   `target_chat_id`: -4984770976
    *   `max_profit_percent`: 3
    *   `image_parser`: `true` для "Родион TO THE MOON" и "Нестеров в рынке". Для остальных `false` или отсутствует.
4.  **Проверить на дубликаты**: Перед добавлением я проверю, нет ли уже канала с таким же `id` или `title` в `source_channels.json`.
5.  **Добавить и сохранить**: Я добавлю новые, уникальные каналы в конец списка и сохраню обновленный файл `source_channels.json`.

## Проверенные пути

*   `./source_channels.json` - будет прочитан и изменен.
*   `./tasks/add_new_chats.md` - этот файл. 