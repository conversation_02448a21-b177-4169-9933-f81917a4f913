# Анализ нескольких предыдущих сообщений для поиска торговых сигналов

## Требования пользователя

Добавить опциональный параметр, который будет анализировать не 1 сообщение, а еще n предыдущих сообщений вместе (т.е. если параметр равен 2, то будет проанализировано 3 сообщения LLM для поиска сигнала).

Для канала "NeTrader Roman" добавить этот параметр, равным 2.

## Техническое решение

### 1. Новый параметр конфигурации
- Название: `analyze_previous_messages` 
- Тип: целое число (по умолчанию 0 - анализировать только текущее сообщение)
- Значение: количество дополнительных предыдущих сообщений для анализа

### 2. Изменения в коде

#### 2.1 Обновление документации load_source_channels()
- Добавить описание параметра `analyze_previous_messages`

#### 2.2 Обновление get_channel_config()
- Добавить возврат параметра `analyze_previous_messages` со значением по умолчанию 0

#### 2.3 Создание функции get_previous_messages()
- Загружает n предыдущих сообщений из канала относительно текущего сообщения
- Возвращает список сообщений отсортированный по времени

#### 2.4 Обновление extract_signal_if_needed()
- Проверка параметра `analyze_previous_messages`
- Если параметр > 0, загружает предыдущие сообщения
- Объединяет тексты всех сообщений для анализа
- Передает объединенный текст в signal_analyzer

### 3. Обновление конфигурации
- Добавить `"analyze_previous_messages": 2` для канала "NeTrader Roman."

## Логика работы

1. При получении нового сообщения из канала с `analyze_previous_messages > 0`
2. Загрузить n предыдущих сообщений из того же канала
3. Объединить тексты всех сообщений (предыдущие + текущее)
4. Передать объединенный текст в анализатор сигналов
5. Анализатор обработает весь контекст для поиска торгового сигнала

## Преимущества

- Более точный анализ сигналов за счет контекста
- Возможность находить сигналы, разбитые на несколько сообщений
- Гибкая настройка для каждого канала

## Проверенные пути
- [x] Найден канал "NeTrader Roman." в source_channels.json (строка 1129)
- [x] Изучена текущая структура конфигурации каналов
- [x] Модифицирован код index_v3.py
- [x] Обновлена конфигурация канала

## Выполненные изменения

### 1. Обновлена документация в load_source_channels()
- Добавлено описание параметра `analyze_previous_messages`

### 2. Обновлена функция get_channel_config()
- Добавлена поддержка параметра `analyze_previous_messages` со значением по умолчанию 0
- Обновлены как старый, так и новый форматы конфигурации

### 3. Создана функция get_previous_messages()
- Функция загружает n предыдущих сообщений из канала
- Сортирует сообщения по ID (от старых к новым)
- Включает обработку ошибок и логирование

### 4. Обновлена функция extract_signal_if_needed()
- Добавлена логика загрузки предыдущих сообщений когда `analyze_previous_messages > 0`
- Объединение текстов предыдущих сообщений с текущим в формате [Сообщение ID]: текст
- Использование объединенного текста во всех вызовах signal_analyzer
- Обновлено логирование для отображения контекстной информации

### 5. Обновлена конфигурация канала "NeTrader Roman."
- Добавлен параметр `"analyze_previous_messages": 2`

## Логика работы (реализованная)

1. При получении сообщения из канала "NeTrader Roman." система проверяет параметр `analyze_previous_messages`
2. Если параметр > 0, загружает 2 предыдущих сообщения из того же канала 
3. Объединяет тексты в формате: [Сообщение ID1]: текст1\n\n[Сообщение ID2]: текст2\n\n[Сообщение ID3]: текст3
4. Передает объединенный текст в анализатор сигналов для более точного анализа
5. Логирует количество сообщений в контексте

## Статус
✅ ЗАВЕРШЕНО - Все задачи выполнены, код работает без ошибок