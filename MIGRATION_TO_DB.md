# Миграция с JSON на PostgreSQL

## Обзор изменений

Проект telegram-parser теперь использует PostgreSQL базу данных вместо JSON файла `source_channels.json` для хранения конфигурации каналов.

## Что изменилось

### 1. Новые файлы
- `db_connection.py` - модуль для работы с PostgreSQL
- `migrate_channels_to_db.py` - скрипт миграции данных
- `MIGRATION_TO_DB.md` - эта документация

### 2. Измененные файлы
- `index_v3.py` - теперь загружает каналы из базы данных
- `trading_bot.py` - обновляет статистику в базе данных
- `requirements.txt` - добавлена зависимость `psycopg2-binary`

### 3. Структура данных

Данные теперь хранятся в таблице `channels` со следующими полями:

```sql
CREATE TABLE channels (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    telegram_id VARCHAR(100) UNIQUE NOT NULL,
    forward_type VARCHAR(50) DEFAULT 'custom',
    signal_fn VARCHAR(100) DEFAULT 'signal_analyzer',
    signals_only BOOLEAN DEFAULT TRUE,
    leverage INTEGER DEFAULT 10,
    portfolio_percent FLOAT DEFAULT 0.25,
    open_mode VARCHAR(50) DEFAULT 'default',
    move_stop_to_breakeven BOOLEAN DEFAULT TRUE,
    allow_signals_without_sl_tp BOOLEAN DEFAULT TRUE,
    max_profit_percent FLOAT DEFAULT 10.0,
    review BOOLEAN DEFAULT TRUE,
    position_lifetime VARCHAR(20) DEFAULT '1h',
    target_chat_id BIGINT DEFAULT -4984770976,
    wins INTEGER DEFAULT 0,
    fails INTEGER DEFAULT 0,
    wins_ratio FLOAT DEFAULT 0.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Настройка

### 1. Установка зависимостей

```bash
pip install psycopg2-binary
```

### 2. Настройка переменных окружения

Убедитесь, что в `.env` файле настроены параметры PostgreSQL:

```env
# PostgreSQL Configuration
POSTGRES_HOST=***************
POSTGRES_PORT=5432
POSTGRES_USER=pguser
POSTGRES_PASSWORD=pgpass
POSTGRES_DB=pgdbname
```

### 3. Создание таблиц

Убедитесь, что Django миграции выполнены и таблица `channels` существует в базе данных.

## Миграция данных

### Автоматическая миграция

1. Запустите скрипт миграции:

```bash
cd /Users/<USER>/projects/django-revolution-3/monorepo/packages/telegram-parser
python migrate_channels_to_db.py
```

2. Подтвердите миграцию, когда будет запрошено.

### Ручная миграция

Если нужно мигрировать данные вручную:

1. Подключитесь к базе данных
2. Создайте таблицу `channels` (если не создана Django)
3. Вставьте данные из JSON файла

## Проверка миграции

### 1. Тест подключения

```python
from db_connection import db_connection

# Тестируем подключение
if db_connection.test_connection():
    print("✅ Подключение к базе данных успешно")
else:
    print("❌ Ошибка подключения к базе данных")
```

### 2. Проверка загрузки каналов

```python
from db_connection import load_source_channels_from_db

# Загружаем каналы из базы данных
channels = load_source_channels_from_db()
print(f"Загружено каналов: {len(channels)}")

# Выводим первые 3 канала
for i, (name, config) in enumerate(channels.items()):
    if i >= 3:
        break
    print(f"{name}: {config['id']}")
```

## Обратная совместимость

Код остается совместимым с существующим форматом данных. Функция `load_source_channels()` теперь возвращает данные в том же формате, что и раньше, но загружает их из базы данных.

## Преимущества перехода на PostgreSQL

1. **Централизованное хранение** - данные доступны из разных приложений
2. **Транзакционность** - атомарные операции обновления статистики
3. **Масштабируемость** - легко добавлять новые поля и индексы
4. **Безопасность** - контроль доступа на уровне базы данных
5. **Резервное копирование** - стандартные инструменты PostgreSQL
6. **Мониторинг** - встроенные инструменты анализа производительности

## Устранение неполадок

### Ошибка подключения к базе данных

1. Проверьте настройки в `.env` файле
2. Убедитесь, что PostgreSQL сервер запущен
3. Проверьте доступность порта и права доступа

### Таблица channels не существует

1. Выполните Django миграции:
```bash
cd /Users/<USER>/projects/django-revolution-3/django_sample
python manage.py migrate
```

2. Или создайте таблицу вручную (см. структуру выше)

### Ошибки при обновлении статистики

1. Проверьте логи для деталей ошибки
2. Убедитесь, что канал существует в базе данных
3. Проверьте права доступа к таблице

## Откат изменений

Если нужно вернуться к JSON файлу:

1. Восстановите старую версию `index_v3.py`
2. Восстановите старую версию `trading_bot.py`
3. Удалите `db_connection.py` и `migrate_channels_to_db.py`
4. Удалите `psycopg2-binary` из `requirements.txt`

## Поддержка

При возникновении проблем:

1. Проверьте логи приложения
2. Проверьте подключение к базе данных
3. Убедитесь, что все зависимости установлены
4. Проверьте права доступа к базе данных 