# Анализ рыночных обзоров

## 🎯 Описание

Функция анализа рыночных обзоров позволяет боту автоматически распознавать аналитические посты о рынке и делать их простые репосты, сохраняя оригинальное форматирование и источник.

## ⚙️ Настройка

### 1. Добавление параметра в SOURCE_CHANNELS

```python
SOURCE_CHANNELS = {
    "Аналитический канал": {
        "id": -1001234567890,
        "forward_type": "custom",
        "review": True  # ← Включить анализ обзоров
    }
}
```

### 2. Переменные окружения

Использует те же настройки, что и анализатор новостей:

```env
# Обязательно для работы анализа обзоров
OPENROUTER_API_KEY=your_api_key_here
LLM_ANALYTICS_ENABLED=True
```

## 🔍 Типы распознаваемых обзоров

Анализатор распознает следующие типы рыночных обзоров:

| Тип | Описание | Пример |
|-----|----------|--------|
| `technical_analysis` | Технический анализ | "На дневном графике сформировалась голова-плечи..." |
| `market_outlook` | Прогноз рынка | "В следующие недели ожидаем коррекцию до уровня..." |
| `price_levels` | Обсуждение уровней | "399.85 – 459.00, где фиксировалась прибыль институционалов..." |
| `daily_review` | Ежедневные разборы | "📈Ежедневный разбор📈 03.07.2025" |
| `trend_analysis` | Анализ трендов | "Восходящий тренд сохраняется при удержании выше..." |
| `institutional_analysis` | Институциональная активность | "Крупные участники рынка накапливают позиции..." |

## 🚫 Что НЕ является обзором

- Торговые сигналы (LONG/SHORT с уровнями)
- Простые новости без анализа
- Рекламные сообщения
- Обновления позиций ("Взяли тейк", "Стоп в б/у")

## 🔄 Логика работы

1. **Проверка параметра**: Если `review: false` → анализ пропускается
2. **LLM анализ**: Сообщение отправляется на классификацию
3. **Принятие решения**: 
   - Если `is_review: true` → простой репост
   - Если `is_review: false` → обычная обработка (анализ новостей/сигналов)

## 📊 Примеры конфигурации

### Смешанная настройка
```python
SOURCE_CHANNELS = {
    # Канал с торговыми сигналами И обзорами
    "Премиум канал": {
        "id": -1002667675217,
        "forward_type": "custom",
        "signal_fn": "signal_analyzer",  # Анализ сигналов
        "signals_only": True,            # Только сигналы на вход
        "review": True                   # + анализ обзоров
    },
    
    # Канал только с аналитикой
    "Аналитика рынка": {
        "id": -1001234567890,
        "forward_type": "custom",
        "review": True  # Только анализ обзоров
    },
    
    # Канал без специальной обработки
    "Новости": {
        "id": -1001111111111,
        "forward_type": "custom"
        # review: false по умолчанию
    }
}
```

### Приоритет обработки
1. **Анализ обзора** (если `review: true`)
   - Если обзор → репост → завершение
   - Если не обзор → продолжение обработки
2. **Фильтрация сигналов** (если `signals_only: true`)
   - Если сигнал → обработка → анализ новостей
   - Если не сигнал → пропуск
3. **Анализ новостей** (если включен)

## 🤖 Используемые модели

Анализ обзоров использует те же модели, что и анализатор новостей:

| Модель | Приоритет | Стоимость |
|--------|-----------|-----------|
| `openai/gpt-4o-mini` | ⭐⭐⭐ | $0.15/1M input |
| `openai/gpt-4o` | ⭐⭐ | $2.5/1M input |
| `anthropic/claude-3.5-sonnet` | ⭐⭐ | $3/1M input |

## 📝 Логирование

При работе анализатора обзоров в логах появляются сообщения:

```
📋 Начинаем анализ обзора для сообщения 12345 из Акулы рынка
📋 Обзор найден в Акулы рынка (ID: 12345): Тип: daily_review, Уверенность: 0.89, Модель: openai/gpt-4o-mini
📋 Сообщение 12345 из Акулы рынка определено как обзор - делаем репост
```

Если сообщение не является обзором:
```
📋 Сообщение 12345 из Акулы рынка не является обзором (уверенность: 0.23)
```

## 🧪 Тестирование

Для тестирования функциональности создан тестовый скрипт:

```bash
python test_review_analyzer.py
```

Скрипт проверяет работу анализатора на различных примерах текстов.

## ⚠️ Важные моменты

1. **Требует LLM API**: Функция работает только при наличии `OPENROUTER_API_KEY`
2. **Зависит от аналитики**: Требует `LLM_ANALYTICS_ENABLED=True`
3. **Экономит ресурсы**: Обзоры репостятся без дополнительной обработки
4. **Сохраняет форматирование**: Простой репост сохраняет оригинальный вид сообщения

## 🔄 Взаимодействие с другими функциями

- **signals_only**: Анализ обзоров выполняется ДО проверки signals_only
- **signal_analyzer**: Обзоры репостятся без извлечения JSON сигналов
- **news_analyzer**: Обзоры не проходят анализ настроения и тегов
- **forward_type**: Работает только с `"custom"`, игнорируется для `"repost"`

## 📈 Преимущества

✅ **Автоматическое распознавание** аналитического контента  
✅ **Сохранение форматирования** через простой репост  
✅ **Экономия ресурсов** - обзоры не требуют сложной обработки  
✅ **Гибкая настройка** для каждого канала отдельно  
✅ **Совместимость** с существующими функциями бота 