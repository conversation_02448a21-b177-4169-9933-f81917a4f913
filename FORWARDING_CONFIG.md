# Конфигурация пересылки сообщений

## 🎯 Настройка целевых чатов

### 🆕 НОВАЯ ФУНКЦИОНАЛЬНОСТЬ: Индивидуальные Target Каналы

Теперь каждый source канал может иметь свой собственный target канал! Это позволяет организовать пересылку по типам контента.

#### Индивидуальные target каналы в SOURCE_CHANNELS:
```python
SOURCE_CHANNELS = {
    "Новости": {
        "id": -1001234567890,
        "forward_type": "custom",
        "target_chat_id": -4000000001  # Индивидуальный канал для новостей
    },
    "Торговые сигналы": {
        "id": -1002667675217,
        "forward_type": "custom",
        "signal_fn": "signal_analyzer",
        "target_chat_id": -4000000002  # Индивидуальный канал для сигналов
    }
}
```

#### Глобальный TARGET_CHAT_ID в .env (fallback):
```env
# Используется для каналов без индивидуального target_chat_id
TARGET_CHAT_ID = -4984770976
```

### 🔧 Логика приоритетов:

1. **target_chat_id канала** (если указан) - **высший приоритет**
2. **глобальный TARGET_CHAT_ID** (если target_chat_id не указан)
3. **режим мониторинга** (если ни один не указан)

### Варианты использования:

1. **Полная индивидуализация:**
   ```python
   SOURCE_CHANNELS = {
       "VIP Signals": {
           "id": -1001234567890,
           "target_chat_id": -4000000001  # VIP канал
       },
       "Public News": {
           "id": -1001987654321,
           "target_chat_id": -4000000002  # Публичный канал
       }
   }
   ```

2. **Смешанная конфигурация:**
   ```python
   SOURCE_CHANNELS = {
       "Premium Channel": {
           "id": -1001234567890,
           "target_chat_id": -4000000001  # Индивидуальный
       },
       "Regular Channel": {
           "id": -1001987654321
           # Использует глобальный TARGET_CHAT_ID
       }
   }
   ```

3. **Режим мониторинга для отдельных каналов:**
   ```python
   SOURCE_CHANNELS = {
       "Test Channel": {
           "id": -1001234567890
           # Без target_chat_id + TARGET_CHAT_ID=None → мониторинг
       }
   }
   ```

## 📋 Настройка каналов-источников

Расширенный формат `SOURCE_CHANNELS` позволяет настроить способ пересылки и target канал для каждого source канала:

```python
SOURCE_CHANNELS = {
    "Канал 1": {
        "id": -1001234567890, 
        "forward_type": "custom",
        "target_chat_id": -4000000001  # Индивидуальный target
    },
    "Канал 2": {
        "id": -1001987654321, 
        "forward_type": "repost",
        "target_chat_id": -4000000002  # Индивидуальный target
    },
    "Торговые сигналы": {
        "id": -1002667675217, 
        "forward_type": "custom", 
        "signal_fn": "signal_analyzer",
        "signals_only": True,  # Только сигналы на вход
        "analyze_images": True,  # Анализ изображений для торговых данных
        "target_chat_id": -4000000003  # Специальный канал для сигналов
    },
    "Общий канал": {
        "id": -1001111111111,
        "forward_type": "custom"
        # Без target_chat_id - использует глобальный TARGET_CHAT_ID
    }
}
```

### Типы пересылки:

#### `"custom"` - Кастомная обработка (по умолчанию)
✅ **Включает:**
- LLM анализ новостей (тикеры, теги, настроение)
- Очистка текста от рекламы
- Добавление заголовков с источником
- Скачивание и отправка медиафайлов
- Обработка длинных подписей
- Отправка от своего имени

```python
"MarketTwits": {"id": -1001203560567, "forward_type": "custom"}
```

#### `"repost"` - Простой репост
✅ **Преимущества:**
- Быстрая пересылка без обработки
- Экономия ресурсов и API запросов
- Сохранение оригинального форматирования
- Показывает источник автоматически

```python
"Binance Announcements": {"id": -1001146915409, "forward_type": "repost"}
```

#### Дополнительные опции для торговых каналов:

##### `"signal_fn": "signal_analyzer"` - Анализ торговых сигналов
- Извлекает торговые сигналы в JSON формат
- Добавляет структурированные данные к сообщениям
- Работает только с `"forward_type": "custom"`

##### `"signals_only": true` - Фильтрация сигналов
- Пересылает **ТОЛЬКО** сообщения с торговыми сигналами на вход
- Игнорирует обновления о выходах, тейках, стоп-лоссах

##### `"analyze_images": true` - Анализ изображений (НОВОЕ!)
- Извлекает торговые данные из изображений (графики, таблицы, схемы)
- Дополняет текстовые сигналы данными из картинок (SL, TP, уровни)
- Работает только с `"signal_fn": "signal_analyzer"`
- Уменьшает шум в канале
- Работает только с `"signal_fn": "signal_analyzer"`

```python
"Акулы рынка": {
    "id": -1002667675217,
    "forward_type": "custom",
    "signal_fn": "signal_analyzer",
    "signals_only": True  # Только входы в позицию
}
```

### Примеры конфигурации:

```python
SOURCE_CHANNELS = {
    # Аналитические каналы - полная обработка
    "Full-Time Trading": {"id": -1001292964247, "forward_type": "custom"},
    "MarketTwits": {"id": -1001203560567, "forward_type": "custom"},
    "Coin Post": {"id": -1001132611527, "forward_type": "custom"},
    
    # Официальные каналы - быстрый репост
    "Binance Announcements": {"id": -1001146915409, "forward_type": "repost"},
    "Смартлаб": {"id": -1001441563903, "forward_type": "repost"},
    
    # Без указания типа = "custom" по умолчанию
    "Newsmaker": {"id": -1001461988268},  # автоматически "custom"
}
```

## 🔧 Режимы работы

### 1. Полная пересылка
- `TARGET_CHAT_ID` задан
- Активные каналы в `SOURCE_CHANNELS`
- Сообщения обрабатываются и пересылаются

### 2. Режим мониторинга
- `TARGET_CHAT_ID` не задан или закомментирован
- Сообщения обрабатываются, но не отправляются
- ID сообщений сохраняются для предотвращения повторной обработки
- Увеличенная задержка между циклами (10 секунд вместо 1)
- Полезно для отладки и тестирования

### 3. Смешанный режим
- Разные каналы с разными типами пересылки
- Оптимизация ресурсов под потребности

## 📊 Логирование

При запуске бот покажет текущую конфигурацию:

**Режим с индивидуальными target каналами:**
```
⚙️ Настройки каналов:
   • Crypto News: кастомная обработка → -4000000001 (индивидуальный)
   • Trading Signals: кастомная обработка + анализ сигналов → -4000000002 (индивидуальный)
   • Public News: кастомная обработка → -4984770976 (глобальный)
   • Test Channel: кастомная обработка → мониторинг

📊 Статистика каналов: 2 с индивидуальными target, 1 с глобальным, 0 в режиме мониторинга

✅ Переслано 3 сообщений из Crypto News в -4000000001
✅ Переслано 1 сообщений из Trading Signals в -4000000002
✅ Переслано 2 сообщений из Public News в -4984770976
```

**Режим мониторинга:**
```
⚙️ Настройки каналов:
   • Test Channel: кастомная обработка → мониторинг

📊 Статистика каналов: 0 с индивидуальными target, 0 с глобальным, 1 в режиме мониторинга

🔍 Мониторинг сообщения 342932 из Test Channel: Анализ рынка показывает...
🔍 Обработано в режиме мониторинга: 160 сообщений из Test Channel
Цикл мониторинга завершен. Ожидание 10 секунд...
```

## ⚠️ Важные замечания

1. **Обратная совместимость:** Старые конфигурации продолжают работать
2. **Валидация:** Бот проверяет корректность настроек при запуске
3. **Гибкость:** Можно легко переключаться между режимами
4. **Безопасность:** Отключение пересылки через комментирование TARGET_CHAT_ID

## 🚀 Рекомендации

- **Аналитические каналы:** используйте `"custom"` для получения инсайтов
- **Новостные ленты:** используйте `"repost"` для скорости
- **Тестирование:** временно отключайте `TARGET_CHAT_ID` для отладки
- **Мониторинг:** следите за логами для понимания работы системы 