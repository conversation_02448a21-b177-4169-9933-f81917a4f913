# Статистика Wins/Fails для каналов

## 📊 Обзор функциональности

Система теперь автоматически отслеживает успешность торговых сигналов каждого канала, ведя подсчет позиций, закрытых в плюс (wins) и в минус (fails). Статистика обновляется в реальном времени в файле `source_channels.json`.

## 🎯 Ключевые особенности

### ✅ Что отслеживается:
- **Wins** - позиции закрытые с положительным realized PnL (> 0)
- **Fails** - позиции закрытые с отрицательным или нулевым realized PnL (<= 0)
- **Wins Ratio** - процент успешных сделок (0.0-100.0), автоматически рассчитывается и сохраняется

### 🔄 Когда обновляется статистика:
1. **Take Profit исполнен** - при срабатывании TP ордеров
2. **Stop Loss исполнен** - при срабатывании SL ордеров  
3. **Принудительное закрытие** - закрытие по таймауту позиции
4. **Ручное закрытие** - закрытие пользователем через API/интерфейс
5. **Автоматическое закрытие** - когда позиция уже закрыта на бирже

## 💾 Структура данных

В файле `source_channels.json` для каждого канала добавлены поля:

```json
{
  "channels": {
    "Название канала": {
      // ... существующие поля ...
      "wins": 0,         // Количество успешных позиций
      "fails": 0,        // Количество неуспешных позиций
      "wins_ratio": 0.0  // Процент побед (0.0-100.0)
    }
  }
}
```

## 🧮 Расчет Realized PnL

### Формула расчета:
```python
# Для LONG позиций
pnl_per_unit = exit_price - entry_price

# Для SHORT позиций  
pnl_per_unit = entry_price - exit_price

# Общий PnL
realized_pnl = pnl_per_unit * position_size
```

### Примеры:
**LONG позиция:**
- Вход: $100, Выход: $105, Размер: 1 BTC
- PnL = ($105 - $100) × 1 = **+$5** ✅ WIN

**SHORT позиция:**
- Вход: $100, Выход: $95, Размер: 1 BTC  
- PnL = ($100 - $95) × 1 = **+$5** ✅ WIN

## 📈 Логирование и мониторинг

### В файлах логов:
```
💚 WIN для Тест: 5W/2L (WR: 71.4%)
❤️ FAIL для Акулы рынка: 3W/4L (WR: 42.9%)
```

### В системных логах:
```
[INFO] Обновлена статистика Тест: 5W/2L (71.4%)
[INFO] Обновлена статистика Акулы рынка: 3W/4L (42.9%)
```

## 🔧 Техническая реализация

### Основные методы:

1. **`_calculate_realized_pnl()`** - расчет PnL при закрытии
2. **`_update_channel_stats()`** - обновление статистики в JSON с автоматическим расчетом wins_ratio
3. **Интеграция во все точки закрытия позиций**

### Обработка частичных закрытий:
- При исполнении TP - PnL рассчитывается для частичного объема
- При исполнении SL - PnL рассчитывается для оставшегося объема
- Общий realized_pnl накапливается по всем частичным закрытиям

## 🛡️ Обработка ошибок

### Fallback механизмы:
1. Если цена закрытия недоступна - попытка получить из ticker
2. Если PnL не рассчитан - попытка получить с биржи
3. Если канал не найден в конфигурации - предупреждение в лог
4. При ошибках записи - детальное логирование

### Инициализация полей:
- Поля `wins`, `fails` и `wins_ratio` автоматически создаются со значениями 0, 0, 0.0
- Совместимость со старыми конфигурациями
- `wins_ratio` автоматически пересчитывается при каждом обновлении

## 📊 Использование статистики

### Анализ эффективности каналов:
```python
# Пример чтения статистики
with open('source_channels.json', 'r') as f:
    data = json.load(f)
    
for channel, config in data['channels'].items():
    wins = config.get('wins', 0)
    fails = config.get('fails', 0)
    wins_ratio = config.get('wins_ratio', 0.0)
    total = wins + fails
    
    if total > 0:
        print(f"{channel}: {wins_ratio:.1f}% ({wins}W/{fails}L)")
```

### Фильтрация по эффективности:
- Каналы с WR > 60% - высокая эффективность
- Каналы с WR 40-60% - средняя эффективность  
- Каналы с WR < 40% - низкая эффективность

## 🚀 Преимущества системы

1. **Автоматический учет** - не требует ручного вмешательства
2. **Реальное время** - статистика обновляется сразу при закрытии
3. **Учет всех сценариев** - TP, SL, ручное закрытие, таймаут
4. **Персистентность** - данные сохраняются в файле
5. **Обратная совместимость** - работает со старыми конфигурациями

## ⚡ Производительность

- Минимальное влияние на производительность
- Асинхронные операции записи
- Обновление только при закрытии позиций
- Эффективная обработка JSON файлов

---

**Автор:** AI Assistant  
**Дата:** 2024  
**Версия:** 1.0 