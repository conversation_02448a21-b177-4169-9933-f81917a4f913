# Управление размером позиций на основе процента портфеля

## 🎯 Описание

Новая функциональность позволяет торговым ботам автоматически рассчитывать размер позиций на основе текущего баланса USDT и заданного процента от портфеля для каждого источника сигналов.

## ⚙️ Настройка

### 1. Настройка MAX_PORTFOLIO_USAGE в .env

```env
# Максимальный процент портфеля для торговли (остальное - резерв)
# Например, 30 означает, что максимум 30% баланса будет использовано для торговли
# Остальные 70% останутся в резерве для безопасности
MAX_PORTFOLIO_USAGE=30
```

### 2. Добавление поля `portfolio_percent` в SOURCE_CHANNELS

```python
SOURCE_CHANNELS = {
    "Акулы рынка": {
        "id": -1002667675217, 
        "forward_type": "custom", 
        "signal_fn": "signal_analyzer", 
        "signals_only": True, 
        "max_volume": 500,           # Резервное значение (если portfolio_percent не работает)
        "leverage": 20,
        "portfolio_percent": 5.0     # 5% от текущего баланса USDT
    },
    "GotBit Trades": {
        "id": -1002148391383,
        "forward_type": "custom",
        "signal_fn": "signal_analyzer",
        "signals_only": True,
        "max_volume": 50,
        "leverage": 10,
        "portfolio_percent": 2.0     # 2% от текущего баланса USDT
    }
}
```

### 3. Приоритет параметров

1. **`MAX_PORTFOLIO_USAGE`** - глобальное ограничение на процент портфеля для торговли
2. **`portfolio_percent`** - если задан, используется для расчета размера позиции от доступного баланса
3. **`max_volume`** - используется как резервное значение, если `portfolio_percent` не задан или баланс недостаточен

## 🔄 Логика работы

### 💰 Типы балансов на фьючерсном счете:

- **Свободный баланс** (`free`) - средства, доступные для открытия новых позиций
- **Заблокированный баланс** (`used`) - средства, заблокированные в открытых позициях как залог
- **Общий баланс** (`total`) - общая сумма средств на фьючерсном счете (free + used)

### ⚡ НОВЫЙ алгоритм расчета размера позиции (от общего баланса):

1. **Проверка наличия `portfolio_percent`**
   - Если задан и > 0, переходим к шагу 2
   - Если не задан, используется фиксированный `max_volume`

2. **Получение текущего баланса USDT**
   ```python
   balance = exchange.fetch_balance()
   usdt_balance = balance['USDT']['free']
   ```

3. **Расчет залога от ОБЩЕГО баланса**
   ```python
   margin_usd = usdt_balance * (portfolio_percent / 100.0)
   ```

4. **Проверка лимита MAX_PORTFOLIO_USAGE**
   ```python
   max_allowed_margin = usdt_balance * (max_portfolio_usage / 100.0)
   if margin_usd > max_allowed_margin:
       margin_usd = max_allowed_margin
       # Логируется предупреждение об ограничении
   ```

5. **Применение плеча для номинала позиции**
   ```python
   volume_usd = margin_usd * leverage
   ```

6. **Расчет размера позиции в контрактах**
   ```python
   position_size = volume_usd / entry_price
   ```

7. **Применение ограничений биржи**
   - Минимальный размер позиции
   - Точность (количество знаков после запятой)
   - Увеличение для размещения 3 TP ордеров

## 📊 Примеры расчетов

### Пример 1: Стандартный расчет (без ограничения портфеля)
- **Баланс USDT:** 1000.0
- **MAX_PORTFOLIO_USAGE:** 100% (без ограничений)
- **portfolio_percent:** 5.0%
- **Плечо:** 20x
- **Цена входа:** 50000.0
- **Результат:** 
  - Залог: 1000 × 0.05 = 50 USD (от общего баланса)
  - Лимит: 1000 × 1.00 = 1000 USD (не ограничивает)
  - Номинал позиции: 50 × 20 = 1000 USD
  - Размер позиции: 1000 ÷ 50000 = 0.02 контракта

### Пример 2: С ограничением портфеля (резерв 70%)
- **Баланс USDT:** 1000.0
- **MAX_PORTFOLIO_USAGE:** 30% (70% в резерве)
- **portfolio_percent:** 5.0%
- **Плечо:** 20x
- **Цена входа:** 50000.0
- **Результат:**
  - Запрошенный залог: 1000 × 0.05 = 50 USD (от общего баланса)
  - Лимит: 1000 × 0.30 = 300 USD
  - Фактический залог: 50 USD (не ограничивается)
  - В резерве: 1000 - 300 = 700 USD
  - Номинал позиции: 50 × 20 = 1000 USD
  - Размер позиции: 1000 ÷ 50000 = 0.02 контракта

### Пример 3: Превышение лимита портфеля
- **Баланс USDT:** 1000.0
- **MAX_PORTFOLIO_USAGE:** 30%
- **portfolio_percent:** 40.0% (больше лимита!)
- **Плечо:** 20x
- **Цена входа:** 50000.0
- **Результат:**
  - Запрошенный залог: 1000 × 0.40 = 400 USD (от общего баланса)
  - Лимит: 1000 × 0.30 = 300 USD
  - **Ограничено до лимита:** 300 USD (30% вместо 40%)
  - Номинал позиции: 300 × 20 = 6000 USD
  - Размер позиции: 6000 ÷ 50000 = 0.12 контракта

### Пример 4: Малый баланс
- **Баланс USDT:** 100.0
- **MAX_PORTFOLIO_USAGE:** 30%
- **portfolio_percent:** 5.0%
- **Плечо:** 20x
- **Цена входа:** 50000.0
- **Результат:**
  - Залог: 100 × 0.05 = 5 USD (от общего баланса)
  - Лимит: 100 × 0.30 = 30 USD (не ограничивает)
  - Номинал позиции: 5 × 20 = 100 USD
  - Размер позиции: 100 ÷ 50000 = 0.002 контракта
  - Увеличен до минимума биржи: 0.003 контракта (для 3 TP)

### Пример 4: Нулевой баланс
- **Баланс USDT:** 0.0
- **portfolio_percent:** 5.0%
- **Результат:** Позиция не открывается (размер = 0)

## 🛡️ Защитные механизмы

### 1. Проверка баланса
```python
if usdt_balance <= 0:
    logger.warning(f"⚠️ Недостаточный баланс USDT: {usdt_balance}")
    return 0.0
```

### 2. Резервное значение
Если `portfolio_percent` не задан или равен 0, используется `max_volume`:
```python
if portfolio_percent is None or portfolio_percent <= 0:
    volume_usd = max_volume_usd
```

### 3. Минимальные ограничения биржи
Система автоматически увеличивает размер позиции до минимума, необходимого для:
- Соблюдения минимального размера ордера
- Размещения 3 TP ордеров (каждый должен быть >= минимума)

## 📈 Конфигурация каналов

### Рекомендуемые проценты по типу каналов:

| Тип канала | Рекомендуемый % | Обоснование |
|------------|----------------|-------------|
| **Премиум каналы** (Акулы рынка, Хозяин Биржи) | 3-5% | Высокое качество сигналов |
| **Проверенные каналы** (GotBit, PREMIUM CRYPTO) | 2-3% | Хорошее качество, средний риск |
| **Скальпинг каналы** | 1-2% | Частые сигналы, малый риск на сделку |
| **Новые/тестовые каналы** | 0.5-1% | Непроверенное качество |

### Текущая конфигурация:

```python
SOURCE_CHANNELS = {
    # Премиум каналы (5%)
    "Акулы рынка": {"portfolio_percent": 5.0, "leverage": 20},
    "Хозяин Биржи | Даня": {"portfolio_percent": 5.0, "leverage": 20},
    "Хозяин Биржи": {"portfolio_percent": 5.0, "leverage": 20},
    
    # Основные каналы (2-3%)
    "GotBit Trades": {"portfolio_percent": 2.0, "leverage": 10},
    "PREMIUM CRYPTO": {"portfolio_percent": 2.0, "leverage": 10},
    "Товарищ Ганн": {"portfolio_percent": 2.5, "leverage": 10},
    "Agapov Trade": {"portfolio_percent": 3.0, "leverage": 10},
    
    # Скальпинг и новые каналы (1.5-2%)
    "Скальпинг с Андреем Павловым": {"portfolio_percent": 1.5, "leverage": 10},
    "Школа им. Влада": {"portfolio_percent": 1.5, "leverage": 10},
    "Learn 2 Trade Crypto": {"portfolio_percent": 1.5, "leverage": 10},
    "Trader signals RU": {"portfolio_percent": 1.5, "leverage": 10},
}
```

## 🔍 Мониторинг и логирование

### Логи расчета размера позиции:

```
💰 Общий баланс фьючерсного счета USDT: 445.80 (свободно: 122.35, в позициях: 323.45)
💰 Общий баланс фьючерсного счета: 445.80 USDT, лимит портфеля: 30.0%
💰 Расчет размера позиции: 1.0% от 445.80 USDT = 4.46 USD залог
💰 С плечом 10x: 4.46 USD × 10 = 44.58 USD размер позиции
⚠️ Размер позиции SOL/USDT:USDT увеличен для 3 TP: 3
```

### Telegram уведомление:

```
🤖 ТОРГОВЫЙ БОТ: 📊 НОВАЯ СДЕЛКА ОТКРЫТА

ID сделки: 71726bd5
Канал: Дом Культуры имени Лебедева
Сигнал: LONG SOL/USDT
Цена входа: $148.92
Размер позиции: 3
Номинал позиции: $44.58
Плечо: 10x
ID ордера: 1938991699503296512

💰 БАЛАНС СЧЕТА
Общий баланс фьючерсного счета: $445.80
Свободный USDT: $122.35
Использовано: 1.0% от общего баланса ($4.46 залог)
Доступно для торговли: $133.74 (30.0%)
В резерве: $312.06 (70.0%)
```

### Информация в bots.md:

```markdown
## Акулы рынка

- **Статус:** ✅ Активен
- **Процент портфеля:** 5.0%
- **Максимальный объем:** $500
- **Плечо:** 20x
- **Функция бота:** default
```

## 🧪 Тестирование

Создан тестовый скрипт `test_portfolio_percent.py` для проверки:

```bash
python test_portfolio_percent.py
```

Тест проверяет:
- ✅ Фиксированный объем (старая логика)
- ✅ Расчет процента от портфеля
- ✅ Обработка малого баланса
- ✅ Обработка нулевого баланса
- ✅ Применение минимальных ограничений биржи

## 🚀 Преимущества

1. **Автоматическое масштабирование** - размер позиций адаптируется к росту/падению портфеля
2. **Гибкое управление рисками** - разные проценты для разных источников
3. **Защита от переторговли** - позиции не открываются при недостаточном балансе
4. **Совместимость** - сохранена поддержка фиксированных объемов
5. **Прозрачность** - подробное логирование всех расчетов

## ⚠️ Важные замечания

1. **MAX_PORTFOLIO_USAGE** - глобальное ограничение защищает резерв от торговли
2. **Баланс обновляется в реальном времени** - каждый сигнал использует актуальный баланс
3. **Резервное значение** - `max_volume` остается как fallback
4. **Минимальные ограничения** - система может увеличить размер позиции для соблюдения требований биржи
5. **Плечо учитывается в расчете** - процент от доступного баланса определяет залог, а плечо увеличивает номинал позиции
6. **Безопасность** - резерв никогда не используется для торговли, обеспечивая финансовую защиту

## 📱 Уведомления в Telegram

### Настройка TARGET чата

Система автоматически отправляет подробные уведомления о торговых операциях в указанный Telegram чат.

#### Конфигурация в .env:
```env
TARGET_CHAT_ID = -1234567890  # ID вашего чата для уведомлений
```

### Типы уведомлений

#### 1. Открытие сделки
```
🤖 ТОРГОВЫЙ БОТ: 📊 НОВАЯ СДЕЛКА ОТКРЫТА

ID сделки: trade-abc123
Канал: Акулы рынка
Сигнал: LONG BTCUSDT
Цена входа: $50000.0
Размер позиции: 0.15
Номинал позиции: $7500.00
Плечо: 20x
ID ордера: 789456123

💰 БАЛАНС СЧЕТА
Свободный USDT: $25000.00
Использовано: 5.0% от доступного портфеля ($375.00 залог)
Доступно для торговли: $7500.00 (30%)
В резерве: $17500.00 (70%)

📋 УСТАНОВЛЕННЫЕ ОРДЕРА
🛡️ Стоп-лосс: ✅ Установлен @ $48000.0
   ID: sl_789456124
💰 Тейк-профиты (3):
   TP1: ✅ @ $52000.0
   ID: tp1_789456125
   TP2: ✅ @ $54000.0
   ID: tp2_789456126
   TP3: ✅ @ $56000.0
   ID: tp3_789456127
```

#### 2. Закрытие сделки
```
🤖 ТОРГОВЫЙ БОТ: 🔒 СДЕЛКА ЗАКРЫТА

ID сделки: trade-abc123
Канал: Акулы рынка
Сигнал: LONG BTCUSDT
Причина закрытия: Take Profit 1 hit
Цена входа: $50000.0
Размер позиции: 0.5
Номинал позиции: $25000.00
💚 Realized PnL: $500.00
Длительность: 2:34:12

💰 Текущий баланс USDT: $25500.00
```

### Информация в уведомлениях

#### При открытии сделки:
- **Основная информация**: ID сделки, канал-источник, направление и символ
- **Параметры входа**: Цена, размер позиции, объем в USD, плечо
- **Баланс счета**: Текущий свободный USDT, процент использования
- **Установленные ордера**: Статус SL и TP с ID ордеров

#### При закрытии сделки:
- **Результат**: Причина закрытия, realized PnL
- **Статистика**: Длительность сделки
- **Баланс**: Обновленный баланс после закрытия

### Статусы ордеров

- **✅ Установлен** - ордер успешно размещен на бирже
- **⚠️ Локально** - ордер сохранен локально (проблема с биржей)
- **❌ Не установлен** - ордер не создан

## 🛡️ Управление рисками с MAX_PORTFOLIO_USAGE

### Рекомендуемые настройки:

| Уровень риска | MAX_PORTFOLIO_USAGE | Описание |
|---------------|-------------------|----------|
| **Консервативный** | 20-30% | Большая часть средств в безопасности |
| **Умеренный** | 40-50% | Баланс между торговлей и безопасностью |
| **Агрессивный** | 60-80% | Больше средств в торговле |
| **Максимальный** | 100% | Без ограничений (не рекомендуется) |

### Преимущества ограничения портфеля:

1. **Защита капитала** - резерв никогда не затрагивается торговлей
2. **Контроль рисков** - ограничение максимальных потерь
3. **Психологический комфорт** - знание о защищенном резерве
4. **Масштабируемость** - возможность увеличения лимитов при росте опыта

### Пример эволюции настроек:

```env
# Начальная настройка (консервативная)
MAX_PORTFOLIO_USAGE=20

# После месяца успешной торговли
MAX_PORTFOLIO_USAGE=30

# При стабильной прибыльности
MAX_PORTFOLIO_USAGE=50
```

## 🔄 Миграция

Для существующих конфигураций:
1. **Добавьте `MAX_PORTFOLIO_USAGE=30` в .env файл** (рекомендуемое начальное значение)
2. Добавьте поле `portfolio_percent` к нужным каналам
3. Оставьте `max_volume` как резервное значение
4. Настройте `TARGET_CHAT_ID` для получения уведомлений
5. Протестируйте в тестовой среде (`BINGX_TESTNET=True`)
6. Постепенно переводите каналы на новую систему
7. Мониторьте результаты и корректируйте `MAX_PORTFOLIO_USAGE` при необходимости 