import os
import json
import asyncio
import base64
from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Optional, Literal, Union
from enum import Enum
from pathlib import Path

from pydantic import BaseModel, Field, ConfigDict
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from dotenv import load_dotenv
import httpx
import ccxt

load_dotenv()


class SignalDirection(str, Enum):
    """Направление торгового сигнала"""
    LONG = "Long"
    SHORT = "Short"


class TradingSignal(BaseModel):
    """Модель торгового сигнала"""
    model_config = ConfigDict(
        validate_assignment=True,
        str_strip_whitespace=True,
        extra='forbid'
    )

    direction: SignalDirection = Field(
        description="Направление сделки: Long или Short"
    )
    ticker: str = Field(
        description="Торговая пара (например: BTC/USDT, ETH/USDT)"
    )
    entry_price: float = Field(
        ge=0,
        description="Цена входа в позицию"
    )
    entry_price_now: Optional[float] = Field(
        default=None,
        ge=0,
        description="Текущая рыночная цена для выставления лимитных ордеров"
    )
    leverage: Optional[int] = Field(
        default=None,
        ge=1,
        le=100,
        description="Плечо для сделки (1-100x)"
    )
    take_profits: Optional[List[float]] = Field(
        default=None,
        description="Уровни взятия прибыли (тейки)"
    )
    stop_loss: Optional[float] = Field(
        default=None,
        ge=0,
        description="Уровень стоп-лосса"
    )
    deposit_percentage: Optional[float] = Field(
        default=None,
        ge=0.1,
        le=100,
        description="Процент от депозита для сделки"
    )
    timestamp: Optional[str] = Field(default=None, description="Время публикации оригинального сообщения")
    timestamp_msk: Optional[str] = Field(default=None, description="Время публикации в московском часовом поясе (UTC+3)")
    channel: Optional[str] = Field(default=None, description="Название канала-источника сигнала")


class SignalAnalyzer:
    """Анализатор торговых сигналов с использованием PydanticAI"""

    def __init__(self, logger=None):
        self.api_key = os.getenv('OPENROUTER_API_KEY')
        if not self.api_key:
            raise ValueError("OPENROUTER_API_KEY не найден в переменных окружения")

        self.logger = logger

        # Модели для анализа сигналов (платные, более точные)
        self.preferred_models = [
            "openai/gpt-4o-mini",           # $0.15/$0.6 per 1M tokens
            "openai/gpt-4o",                # $2.5/$10 per 1M tokens  
            "openai/gpt-4-turbo",           # $10/$30 per 1M tokens
            "anthropic/claude-3.5-sonnet",  # $3/$15 per 1M tokens
            "anthropic/claude-3-opus",      # $15/$75 per 1M tokens
            "google/gemini-pro-1.5",        # $1.25/$5 per 1M tokens
        ]

        # Базовый system prompt (будет модифицироваться в зависимости от настроек канала)
        self.base_system_prompt = (
            'You are a professional trading signal parser. Parse trading signals from Russian text and return ONLY a valid JSON object. '
            'Extract trading information including direction (Long/Short), ticker, leverage, entry price, take profits, stop loss, and deposit percentage. '
            'Return ONLY the JSON analysis without any other text. '
        )

        # Создаем агента с базовым промптом (будет обновляться динамически)
        self.agent = Agent[str, str](system_prompt=self.base_system_prompt)

    def _encode_image_to_base64(self, image_path: Union[str, Path]) -> Optional[str]:
        """Кодирует изображение в base64 для отправки в LLM"""
        try:
            image_path = Path(image_path)
            if not image_path.exists():
                if self.logger:
                    asyncio.create_task(self.logger.error(f"Изображение не найдено: {image_path}"))
                return None
            
            # Читаем файл и кодируем в base64
            with open(image_path, 'rb') as image_file:
                image_data = image_file.read()
                base64_encoded = base64.b64encode(image_data).decode('utf-8')
                
            if self.logger:
                asyncio.create_task(self.logger.info(f"Изображение закодировано: {image_path.name} ({len(image_data)} байт)"))
                
            return base64_encoded
            
        except Exception as e:
            if self.logger:
                asyncio.create_task(self.logger.error(f"Ошибка кодирования изображения {image_path}: {str(e)}", e))
            return None

    def _get_image_mime_type(self, image_path: Union[str, Path]) -> str:
        """Определяет MIME тип изображения по расширению"""
        image_path = Path(image_path)
        extension = image_path.suffix.lower()
        
        mime_types = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg', 
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.webp': 'image/webp'
        }
        
        return mime_types.get(extension, 'image/jpeg')  # По умолчанию JPEG

    def _get_vision_system_prompt(self, allow_without_sl_tp: bool = False) -> str:
        """Генерирует system prompt для анализа изображений с торговыми сигналами"""
        
        base_prompt = (
            'You are a professional trading signal analyzer with computer vision capabilities. '
            'Analyze the provided image AND text to extract complete trading signal information. '
            'The image may contain charts, tables, or visual representations of trading signals with entry points, stop losses, and take profits. '
            'Combine information from both the text and the image to create a complete trading signal. '
            'Return ONLY a valid JSON object with the extracted data. '
        )
        
        if allow_without_sl_tp:
            return (
                base_prompt +
                'REQUIRED FIELDS FOR VALID SIGNAL: direction, ticker - these MUST be present for valid trading signal. '
                'OPTIONAL FIELDS: entry_price, leverage, deposit_percentage, stop_loss, take_profits - extract from image or text if available. '
                'IMPORTANT: If direction or ticker is missing from both text and image, return {} (empty object). '
                'Look for trading data in the image: price levels, support/resistance lines, entry/exit points, stop loss levels, take profit targets. '
                'Example format: '
                '{"direction": "Short", "ticker": "BTC/USDT", "entry_price": 45000.50, "leverage": 15, "take_profits": [44500, 44000], "stop_loss": 46000, "deposit_percentage": 3.0} '
                'OR minimal signal: {"direction": "Long", "ticker": "ETH/USDT"} '
                'OR for non-trading content: {} '
                'Guidelines for IMAGE analysis:'
                '1. Look for CHARTS with marked entry points, support/resistance levels'
                '2. Find TABLES with trading parameters (Entry, SL, TP1, TP2, etc.)'
                '3. Identify PRICE LEVELS marked on graphs or in text overlays'
                '4. Extract NUMERICAL VALUES for prices, percentages, ratios'
                '5. Recognize TRADING SYMBOLS/TICKERS (BTC, ETH, etc.)'
                '6. Identify DIRECTION indicators (arrows, colors, text like "Long", "Short", "Buy", "Sell")'
                'Guidelines for TEXT analysis:'
                '1. DIRECTION: Extract "Long" or "Short" (exact case). Convert лонг/шорт to Long/Short.'
                '2. TICKER: Extract trading pair like BTC/USDT, ETH/USDT, etc. If only coin name given, add "/USDT".'
                '3. ENTRY_PRICE: Extract entry price as number if present in text or image.'
                '4. STOP_LOSS: Extract stop loss level as number (стоп) from text or image.'
                '5. TAKE_PROFITS: Extract take profit levels as array of numbers (тейки) from text or image.'
                '6. LEVERAGE: Extract leverage multiplier (like 15x -> 15, 20x -> 20).'
                '7. DEPOSIT_PERCENTAGE: Extract deposit percentage (like "до 3% от депозита" -> 3.0).'
                'CRITICAL RULES: '
                '- Prioritize IMAGE data over text when both are available'
                '- Return {} if no trading signal found in either text or image'
                '- For "btc short" return {"direction": "Short", "ticker": "BTC/USDT"}'
                '- Return ONLY a valid JSON object, no other text'
                '- Use exact field names from the schema'
                '- Numbers should be clean floats/integers without text'
            )
        else:
            return (
                base_prompt +
                'REQUIRED FIELDS FOR VALID SIGNAL: direction, ticker, entry_price, stop_loss, take_profits (minimum 2) - ALL MUST be present. '
                'OPTIONAL FIELDS: leverage, deposit_percentage - extract if available. '
                'IMPORTANT: Look for complete trading setup in the image - entry point, stop loss, and multiple take profit levels. '
                'If any required field is missing from both text and image, return {} (empty object). '
                'Example format: '
                '{"direction": "Short", "ticker": "BTC/USDT", "entry_price": 45000.50, "leverage": 15, "take_profits": [44500, 44000, 43500], "stop_loss": 46000, "deposit_percentage": 3.0} '
                'OR for incomplete signals: {} '
                'Guidelines for IMAGE analysis:'
                '1. CHARTS: Look for marked entry, SL, and TP levels on price charts'
                '2. TABLES: Find structured data with Entry/SL/TP1/TP2/TP3 columns'
                '3. ANNOTATIONS: Read text overlays, arrows, and price markers'
                '4. LEVELS: Identify horizontal lines marking important price levels'
                '5. PATTERNS: Recognize visual trading setups and formations'
                'Guidelines for combining TEXT + IMAGE:'
                '1. Use IMAGE for precise price levels (entry, SL, TPs)'
                '2. Use TEXT for direction, ticker, and general context'
                '3. Prioritize numerical values from IMAGE over text estimates'
                '4. Ensure all required fields are present before returning signal'
                'CRITICAL RULES: '
                '- Return {} if ANY required field is missing from both sources'
                '- IMAGE data takes priority for price levels'
                '- Must have at least 2 take profit levels'
                '- Return ONLY a valid JSON object, no other text'
            )

    def _create_openrouter_model(self, model_name: str) -> OpenAIModel:
        """Создает модель OpenRouter для использования с PydanticAI"""
        # Используем стандартный HTTP клиент без прокси для платных моделей (они обычно более стабильны)
        provider = OpenAIProvider(
            api_key=self.api_key,
            base_url='https://openrouter.ai/api/v1'
        )
        return OpenAIModel(model_name=model_name, provider=provider)

    def _get_system_prompt(self, allow_without_sl_tp: bool = False) -> str:
        """Генерирует system prompt в зависимости от настроек канала"""

        if allow_without_sl_tp:
            # Режим с разрешенными сигналами без SL/TP
            return (
                self.base_system_prompt +
                'REQUIRED FIELDS FOR VALID SIGNAL: direction, ticker - these MUST be present for valid trading signal. '
                'OPTIONAL FIELDS: entry_price, leverage, deposit_percentage, stop_loss, take_profits - omit if not found. '
                'IMPORTANT: If direction or ticker is missing, return {} (empty object) - this is NOT a trading signal! '
                'If entry_price, stop_loss or take_profits are missing, still return the signal - they will be auto-generated. '
                'Example format: '
                '{"direction": "Short", "ticker": "BTC/USDT", "entry_price": 45000.50, "leverage": 15, "take_profits": [44500, 44000], "stop_loss": 46000, "deposit_percentage": 3.0} '
                'OR minimal signal: {"direction": "Long", "ticker": "ETH/USDT"} '
                'OR signal with entry: {"direction": "Long", "ticker": "ETH/USDT", "entry_price": 2500.0} '
                'OR for non-trading content or reviews: {} '
                'Guidelines:'
                '1. DIRECTION: REQUIRED - Extract "Long" or "Short" (exact case). Convert лонг/шорт to Long/Short. '
                '2. TICKER: REQUIRED - Extract trading pair like BTC/USDT, ETH/USDT, etc. If only coin name given (like "BTC", "ETH", "btc"), automatically add "/USDT" to make it "BTC/USDT", "ETH/USDT".'
                '3. ENTRY_PRICE: OPTIONAL - Extract entry price as number if present.'
                '4. STOP_LOSS: OPTIONAL - Extract stop loss level as number (стоп) if present.'
                '5. TAKE_PROFITS: OPTIONAL - Extract take profit levels as array of numbers (тейки) if present.'
                '6. LEVERAGE: OPTIONAL - Extract leverage multiplier (like 15x -> 15, 20x -> 20).'
                '7. DEPOSIT_PERCENTAGE: OPTIONAL - Extract deposit percentage (like "до 3% от депозита" -> 3.0).'
                'CRITICAL RULES: '
                '- Return {} if ANY of these are true: no direction, no ticker'
                '- Market reviews, analysis posts, or educational content should return {}'
                '- Simple trading signals like "BTC/USDT LONG", "SHORT ETH", "btc long", "eth short" are valid signals'
                '- For "btc short" return {"direction": "Short", "ticker": "BTC/USDT"}'
                '- For "eth long" return {"direction": "Long", "ticker": "ETH/USDT"}'
                '- Return ONLY a valid JSON object, no other text'
                '- DO NOT include original content in your response'
                '- DO NOT add explanations or comments'
                '- Use exact field names from the schema'
                '- Convert Russian terms: лонг->Long, шорт->Short, плечо->leverage, вход->entry_price, тейк->take_profit, стоп->stop_loss'
                '- Numbers should be clean floats/integers without text'
            )
        else:
            # Стандартный режим с обязательными SL/TP
            return (
                self.base_system_prompt +
                'REQUIRED FIELDS FOR VALID SIGNAL: direction, ticker, entry_price, stop_loss, take_profits (minimum 2) - ALL MUST be present for valid trading signal. '
                'OPTIONAL FIELDS: leverage, deposit_percentage - omit if not found. '
                'IMPORTANT: If stop_loss is missing OR take_profits has less than 2 levels, return {} (empty object) - this is NOT a trading signal! '
                'Example format: '
                '{"direction": "Short", "ticker": "BTC/USDT", "entry_price": 45000.50, "leverage": 15, "take_profits": [44500, 44000, 43500], "stop_loss": 46000, "deposit_percentage": 3.0} '
                'OR for non-trading content or reviews: {} '
                'Guidelines:'
                '1. DIRECTION: REQUIRED - Extract "Long" or "Short" (exact case). Convert лонг/шорт to Long/Short. '
                'If direction is not explicitly stated but take profits and stop loss are present, you MUST infer the direction: '
                '- If entry_price > stop_loss AND entry_price < take_profits, then direction is "Long" '
                '- If entry_price < stop_loss AND entry_price > take_profits, then direction is "Short" '
                '2. TICKER: REQUIRED - Extract trading pair like BTC/USDT, ETH/USDT, etc. Always use USDT pairs if not specified.'
                '3. ENTRY_PRICE: REQUIRED - Extract entry price as number.'
                '4. STOP_LOSS: REQUIRED - Extract stop loss level as number (стоп). Must be present!'
                '5. TAKE_PROFITS: REQUIRED - Extract ALL take profit levels as array of numbers (тейки). Must have at least 2 levels!'
                '6. LEVERAGE: OPTIONAL - Extract leverage multiplier (like 15x -> 15, 20x -> 20).'
                '7. DEPOSIT_PERCENTAGE: OPTIONAL - Extract deposit percentage (like "до 3% от депозита" -> 3.0).'
                'CRITICAL RULES: '
                '- Return {} if ANY of these are true: no direction, no ticker, no entry_price, no stop_loss, less than 2 take_profits'
                '- Market reviews, analysis posts, or educational content should return {}'
                '- Only concrete trading signals with specific entry/exit points should be parsed'
                '- Return ONLY a valid JSON object, no other text'
                '- DO NOT include original content in your response'
                '- DO NOT add explanations or comments'
                '- Use exact field names from the schema'
                '- Convert Russian terms: лонг->Long, шорт->Short, плечо->leverage, вход->entry_price, тейк->take_profit, стоп->stop_loss'
                '- Numbers should be clean floats/integers without text'
            )

    async def extract_signal(self, text: str, message_date=None, channel_name=None, allow_without_sl_tp=False) -> Optional[TradingSignal]:
        """Извлекает торговый сигнал из текста"""
        if not text or len(text.strip()) < 3:
            return None

        # Ограничиваем длину текста для анализа
        text = text[:1000] if len(text) > 1000 else text

        # Форматируем timestamp из времени сообщения
        timestamp_str = None
        timestamp_msk_str = None
        if message_date:
            timestamp_str = message_date.isoformat()
            # Добавляем 3 часа для московского времени
            moscow_time = message_date + timedelta(hours=3)
            timestamp_msk_str = moscow_time.isoformat()

        # Пробуем модели по порядку приоритета
        for model_name in self.preferred_models:
            if self.logger:
                await self.logger.info(f"Пробуем извлечь сигнал с моделью: {model_name}")

            try:
                # Создаем модель для OpenRouter
                openrouter_model = self._create_openrouter_model(model_name)

                # Создаем агент с правильным system prompt для данного канала
                system_prompt = self._get_system_prompt(allow_without_sl_tp)
                agent = Agent[str, str](system_prompt=system_prompt)

                # Запускаем анализ с агентом
                result = await agent.run(
                    f"Parse trading signal and return JSON only: {text}",
                    model=openrouter_model
                )

                if result.output:
                    # Парсим JSON из текстового ответа
                    try:
                        # Очищаем ответ от лишнего текста и извлекаем JSON
                        response_text = result.output.strip()



                        # Ищем JSON в ответе
                        json_start = response_text.find('{')
                        json_end = response_text.rfind('}') + 1

                        if json_start >= 0 and json_end > json_start:
                            json_str = response_text[json_start:json_end]
                            signal_data = json.loads(json_str)

                            # Если JSON пустой, значит это не торговый сигнал
                            if not signal_data:
                                return None

                            # Проверяем наличие обязательных полей
                            direction = signal_data.get('direction')
                            ticker = signal_data.get('ticker')
                            entry_price = signal_data.get('entry_price')
                            stop_loss = signal_data.get('stop_loss')
                            take_profits = signal_data.get('take_profits')

                            # Проверяем базовые обязательные поля
                            if not allow_without_sl_tp:
                                # Стандартный режим - все поля обязательны
                                if not direction or not ticker or entry_price is None:
                                    return None  # Не торговый сигнал - отсутствуют базовые поля
                            else:
                                # Гибкий режим - только direction и ticker обязательны
                                if not direction or not ticker:
                                    return None  # Не торговый сигнал - отсутствуют базовые поля

                            # ВАЖНО: Проверяем наличие stop_loss и минимум 2 take_profit
                            if not allow_without_sl_tp:
                                # Стандартная проверка - SL и TP обязательны
                                if stop_loss is None:
                                    if self.logger:
                                        await self.logger.info(f"🚫 Сообщение не содержит stop_loss - это обзор, не сигнал")
                                    return None  # Обзор/анализ без stop_loss

                                if not take_profits or not isinstance(take_profits, list) or len(take_profits) < 2:
                                    if self.logger:
                                        tp_count = len(take_profits) if take_profits and isinstance(take_profits, list) else 0
                                        await self.logger.info(f"🚫 Сообщение содержит {tp_count} take_profit(s), требуется минимум 2 - это обзор, не сигнал")
                                    return None  # Обзор/анализ без достаточного количества take_profit
                            else:
                                # Разрешены сигналы без SL/TP - создаем дефолтные значения
                                if entry_price is None or stop_loss is None or not take_profits or not isinstance(take_profits, list) or len(take_profits) < 1:
                                    if self.logger:
                                        await self.logger.info(f"⚙️ Создаем дефолтные значения для сигнала без полных данных")
                                    entry_price, stop_loss, take_profits = await self._create_default_values(direction, ticker, entry_price, stop_loss, take_profits)
                                    signal_data['entry_price'] = entry_price
                                    signal_data['stop_loss'] = stop_loss
                                    signal_data['take_profits'] = take_profits

                            # Создаем объект TradingSignal из распарсенного JSON
                            signal = TradingSignal(
                                direction=direction,
                                ticker=ticker,
                                entry_price=entry_price,
                                leverage=signal_data.get('leverage'),
                                take_profits=signal_data.get('take_profits'),
                                stop_loss=signal_data.get('stop_loss'),
                                deposit_percentage=signal_data.get('deposit_percentage'),
                                timestamp=timestamp_str,
                                timestamp_msk=timestamp_msk_str,
                                channel=channel_name
                            )

                            # Получаем текущую рыночную цену для entry_price_now
                            try:
                                current_price = await self._get_market_price(ticker, direction)
                                if current_price:
                                    signal.entry_price_now = current_price
                                    if self.logger:
                                        await self.logger.info(f"💰 Получена текущая рыночная цена {ticker}: {current_price}")
                                else:
                                    if self.logger:
                                        await self.logger.warning(f"⚠️ Не удалось получить текущую цену для {ticker}")
                            except Exception as e:
                                if self.logger:
                                    await self.logger.warning(f"⚠️ Ошибка получения текущей цены для {ticker}: {e}")

                            return signal
                        else:
                            # Если JSON не найден, пробуем парсить весь ответ
                            signal_data = json.loads(response_text)
                            if not signal_data:
                                return None

                            # Проверяем наличие обязательных полей
                            direction = signal_data.get('direction')
                            ticker = signal_data.get('ticker')
                            entry_price = signal_data.get('entry_price')
                            stop_loss = signal_data.get('stop_loss')
                            take_profits = signal_data.get('take_profits')

                            # Проверяем базовые обязательные поля
                            if not allow_without_sl_tp:
                                # Стандартный режим - все поля обязательны
                                if not direction or not ticker or entry_price is None:
                                    return None  # Не торговый сигнал - отсутствуют базовые поля
                            else:
                                # Гибкий режим - только direction и ticker обязательны
                                if not direction or not ticker:
                                    return None  # Не торговый сигнал - отсутствуют базовые поля

                            # ВАЖНО: Проверяем наличие stop_loss и минимум 2 take_profit
                            if not allow_without_sl_tp:
                                # Стандартная проверка - SL и TP обязательны
                                if stop_loss is None:
                                    if self.logger:
                                        await self.logger.info(f"🚫 Сообщение не содержит stop_loss - это обзор, не сигнал")
                                    return None  # Обзор/анализ без stop_loss

                                if not take_profits or not isinstance(take_profits, list) or len(take_profits) < 2:
                                    if self.logger:
                                        tp_count = len(take_profits) if take_profits and isinstance(take_profits, list) else 0
                                        await self.logger.info(f"🚫 Сообщение содержит {tp_count} take_profit(s), требуется минимум 2 - это обзор, не сигнал")
                                    return None  # Обзор/анализ без достаточного количества take_profit
                            else:
                                # Разрешены сигналы без SL/TP - создаем дефолтные значения
                                if entry_price is None or stop_loss is None or not take_profits or not isinstance(take_profits, list) or len(take_profits) < 1:
                                    if self.logger:
                                        await self.logger.info(f"⚙️ Создаем дефолтные значения для сигнала без полных данных")
                                    entry_price, stop_loss, take_profits = await self._create_default_values(direction, ticker, entry_price, stop_loss, take_profits)
                                    signal_data['entry_price'] = entry_price
                                    signal_data['stop_loss'] = stop_loss
                                    signal_data['take_profits'] = take_profits

                            signal = TradingSignal(
                                direction=direction,
                                ticker=ticker,
                                entry_price=entry_price,
                                leverage=signal_data.get('leverage'),
                                take_profits=signal_data.get('take_profits'),
                                stop_loss=signal_data.get('stop_loss'),
                                deposit_percentage=signal_data.get('deposit_percentage'),
                                timestamp=timestamp_str,
                                timestamp_msk=timestamp_msk_str,
                                channel=channel_name
                            )

                            # Получаем текущую рыночную цену для entry_price_now
                            try:
                                current_price = await self._get_market_price(ticker, direction)
                                if current_price:
                                    signal.entry_price_now = current_price
                                    if self.logger:
                                        await self.logger.info(f"💰 Получена текущая рыночная цена {ticker}: {current_price}")
                                else:
                                    if self.logger:
                                        await self.logger.warning(f"⚠️ Не удалось получить текущую цену для {ticker}")
                            except Exception as e:
                                if self.logger:
                                    await self.logger.warning(f"⚠️ Ошибка получения текущей цены для {ticker}: {e}")

                            return signal

                    except json.JSONDecodeError as je:
                        if self.logger:
                            await self.logger.warning(f"Модель {model_name} вернула неверный JSON формат: {je}. Ответ: {result.output[:200]}")
                        continue  # Пробуем следующую модель

            except Exception as e:
                error_msg = str(e)
                if self.logger:
                    if "rate limit" in error_msg.lower() or "quota" in error_msg.lower():
                        await self.logger.warning(f"Модель {model_name} заблокирована лимитами API: {error_msg}")
                    elif "insufficient funds" in error_msg.lower() or "credits" in error_msg.lower():
                        await self.logger.warning(f"Недостаточно средств для модели {model_name}: {error_msg}")
                    else:
                        await self.logger.error(f"Ошибка при анализе сигнала с моделью {model_name}: {error_msg}", e)

                continue  # Пробуем следующую модель

        if self.logger:
            await self.logger.warning("Не удалось извлечь торговый сигнал ни одной из доступных моделей")

        return None

    async def extract_signal_with_image(self, text: str, image_path: Union[str, Path], message_date=None, channel_name=None, allow_without_sl_tp=False) -> Optional[TradingSignal]:
        """Извлекает торговый сигнал из текста и изображения используя vision-модели"""
        if not text and not image_path:
            return None

        # Проверяем существование изображения
        if image_path and not Path(image_path).exists():
            if self.logger:
                await self.logger.error(f"Изображение не найдено: {image_path}")
            return None

        # Ограничиваем длину текста для анализа
        if text:
            text = text[:1000] if len(text) > 1000 else text

        # Форматируем timestamp из времени сообщения
        timestamp_str = None
        timestamp_msk_str = None
        if message_date:
            timestamp_str = message_date.isoformat()
            # Добавляем 3 часа для московского времени
            moscow_time = message_date + timedelta(hours=3)
            timestamp_msk_str = moscow_time.isoformat()

        # Кодируем изображение в base64
        image_base64 = None
        image_mime_type = None
        if image_path:
            image_base64 = self._encode_image_to_base64(image_path)
            if not image_base64:
                if self.logger:
                    await self.logger.error(f"Не удалось закодировать изображение: {image_path}")
                # Fallback на обычный анализ текста
                return await self.extract_signal(text, message_date, channel_name, allow_without_sl_tp)
            
            image_mime_type = self._get_image_mime_type(image_path)

        # Пробуем модели по порядку приоритета (только vision-модели)
        vision_models = [
            "openai/gpt-4o",                # Лучшая vision модель
            "openai/gpt-4o-mini",           # Экономичная vision модель
            "anthropic/claude-3.5-sonnet",  # Отличная vision модель
        ]

        for model_name in vision_models:
            if self.logger:
                await self.logger.info(f"🖼️ Пробуем анализ изображения с моделью: {model_name}")

            try:
                # Создаем модель для OpenRouter
                openrouter_model = self._create_openrouter_model(model_name)

                # Создаем агент с vision system prompt
                vision_system_prompt = self._get_vision_system_prompt(allow_without_sl_tp)
                vision_agent = Agent[str, str](system_prompt=vision_system_prompt)

                # Формируем промпт с текстом и изображением
                user_prompt = f"Analyze trading signal from text and image:\n\nTEXT: {text or 'No text provided'}\n\nExtract complete trading signal data and return JSON only."

                # Для PydanticAI нужно использовать специальный формат для изображений
                # Временно используем прямой API вызов через httpx
                headers = {
                    'Authorization': f'Bearer {self.api_key}',
                    'Content-Type': 'application/json'
                }

                # Формируем запрос в формате OpenAI API
                messages = [
                    {
                        "role": "system",
                        "content": vision_system_prompt
                    },
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": user_prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:{image_mime_type};base64,{image_base64}"
                                }
                            }
                        ]
                    }
                ]

                payload = {
                    "model": model_name,
                    "messages": messages,
                    "max_tokens": 1000,
                    "temperature": 0.1
                }

                async with httpx.AsyncClient(timeout=60.0) as client:
                    response = await client.post(
                        'https://openrouter.ai/api/v1/chat/completions',
                        headers=headers,
                        json=payload
                    )

                if response.status_code == 200:
                    result_data = response.json()
                    result_content = result_data['choices'][0]['message']['content']

                    if result_content:
                        # Парсим JSON из текстового ответа
                        try:
                            # Очищаем ответ от лишнего текста и извлекаем JSON
                            response_text = result_content.strip()

                            # Ищем JSON в ответе
                            json_start = response_text.find('{')
                            json_end = response_text.rfind('}') + 1

                            if json_start >= 0 and json_end > json_start:
                                json_str = response_text[json_start:json_end]
                                signal_data = json.loads(json_str)

                                # Если JSON пустой, значит это не торговый сигнал
                                if not signal_data:
                                    if self.logger:
                                        await self.logger.info(f"🖼️ Модель {model_name} не обнаружила торгового сигнала в изображении")
                                    return None

                                # Проверяем наличие обязательных полей
                                direction = signal_data.get('direction')
                                ticker = signal_data.get('ticker')
                                entry_price = signal_data.get('entry_price')
                                stop_loss = signal_data.get('stop_loss')
                                take_profits = signal_data.get('take_profits')

                                if not direction or not ticker:
                                    if self.logger:
                                        await self.logger.warning(f"🖼️ Модель {model_name}: отсутствуют обязательные поля (direction или ticker)")
                                    continue  # Пробуем следующую модель

                                # Проверяем режим работы канала
                                if not allow_without_sl_tp:
                                    # Строгий режим - требуются все поля
                                    if entry_price is None or stop_loss is None or not take_profits or not isinstance(take_profits, list) or len(take_profits) < 2:
                                        if self.logger:
                                            await self.logger.warning(f"🖼️ Модель {model_name}: неполные данные сигнала (требуются entry_price, stop_loss, take_profits)")
                                        continue  # Пробуем следующую модель
                                else:
                                    # Разрешены сигналы без SL/TP - создаем дефолтные значения
                                    if entry_price is None or stop_loss is None or not take_profits or not isinstance(take_profits, list) or len(take_profits) < 1:
                                        if self.logger:
                                            await self.logger.info(f"🖼️ Создаем дефолтные значения для сигнала из изображения")
                                        entry_price, stop_loss, take_profits = await self._create_default_values(direction, ticker, entry_price, stop_loss, take_profits)
                                        signal_data['entry_price'] = entry_price
                                        signal_data['stop_loss'] = stop_loss
                                        signal_data['take_profits'] = take_profits

                                # Создаем объект TradingSignal из распарсенного JSON
                                signal = TradingSignal(
                                    direction=direction,
                                    ticker=ticker,
                                    entry_price=entry_price,
                                    leverage=signal_data.get('leverage'),
                                    take_profits=signal_data.get('take_profits'),
                                    stop_loss=signal_data.get('stop_loss'),
                                    deposit_percentage=signal_data.get('deposit_percentage'),
                                    timestamp=timestamp_str,
                                    timestamp_msk=timestamp_msk_str,
                                    channel=channel_name
                                )

                                # Получаем текущую рыночную цену для entry_price_now
                                try:
                                    current_price = await self._get_market_price(ticker, direction)
                                    if current_price:
                                        signal.entry_price_now = current_price
                                        if self.logger:
                                            await self.logger.info(f"💰 Получена текущая рыночная цена {ticker}: {current_price}")
                                    else:
                                        if self.logger:
                                            await self.logger.warning(f"⚠️ Не удалось получить текущую цену для {ticker}")
                                except Exception as e:
                                    if self.logger:
                                        await self.logger.warning(f"⚠️ Ошибка получения текущей цены для {ticker}: {e}")

                                if self.logger:
                                    await self.logger.success(f"🖼️ Торговый сигнал извлечен из изображения с помощью {model_name}: {direction} {ticker}")

                                return signal

                        except json.JSONDecodeError as je:
                            if self.logger:
                                await self.logger.warning(f"🖼️ Модель {model_name} вернула неверный JSON формат: {je}")
                            continue  # Пробуем следующую модель
                else:
                    if self.logger:
                        await self.logger.warning(f"🖼️ Ошибка API {model_name}: {response.status_code} - {response.text}")
                    continue

            except Exception as e:
                error_msg = str(e)
                if self.logger:
                    if "rate limit" in error_msg.lower() or "quota" in error_msg.lower():
                        await self.logger.warning(f"🖼️ Модель {model_name} заблокирована лимитами API: {error_msg}")
                    elif "insufficient funds" in error_msg.lower() or "credits" in error_msg.lower():
                        await self.logger.warning(f"🖼️ Недостаточно средств для модели {model_name}: {error_msg}")
                    else:
                        await self.logger.error(f"🖼️ Ошибка при анализе изображения с моделью {model_name}: {error_msg}", e)

                continue  # Пробуем следующую модель

        if self.logger:
            await self.logger.warning("🖼️ Не удалось извлечь торговый сигнал из изображения ни одной из доступных vision-моделей")

        # Fallback на обычный анализ текста если изображение не помогло
        if text:
            if self.logger:
                await self.logger.info("🔄 Fallback на анализ только текста без изображения")
            return await self.extract_signal(text, message_date, channel_name, allow_without_sl_tp)

        return None

    async def _get_market_price(self, ticker: str, direction: str) -> Optional[float]:
        """Получает актуальную рыночную цену с биржи BingX"""
        try:
            # Создаем временный exchange для получения цены
            api_key = os.getenv('BINGX_API_KEY')
            api_secret = os.getenv('BINGX_API_SECRET')
            testnet = os.getenv('BINGX_TESTNET', 'True').lower() == 'true'

            if not api_key or not api_secret:
                if self.logger:
                    await self.logger.warning("BingX API ключи не настроены, используем статичные цены")
                return None

            exchange = ccxt.bingx({
                'apiKey': api_key,
                'secret': api_secret,
                'sandbox': testnet,
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'future',
                }
            })

            # Нормализуем символ для BingX
            symbol = ticker.replace('/', '-')  # BTC/USDT -> BTC-USDT

            # Получаем ticker с биржи
            ticker_data = exchange.fetch_ticker(symbol)

            if not ticker_data:
                return None

            # Получаем лучшую цену bid/ask
            bid_price = ticker_data.get('bid')  # Цена покупки (лучшая цена, по которой можно купить)
            ask_price = ticker_data.get('ask')  # Цена продажи (лучшая цена, по которой можно продать)
            last_price = ticker_data.get('last')  # Последняя цена

            if direction == "Long":
                # Для лонга берем bid цену (лучшую цену покупки)
                base_price = bid_price or last_price
                if base_price:
                    return base_price
            else:  # Short
                # Для шорта берем ask цену (лучшую цену продажи)
                base_price = ask_price or last_price
                if base_price:
                    return base_price

            return last_price

        except Exception as e:
            if self.logger:
                await self.logger.warning(f"Не удалось получить цену {ticker} с биржи: {e}")
            return None

    async def _create_default_values(self, direction: str, ticker: str, entry_price: Optional[float], stop_loss: Optional[float], take_profits: Optional[List[float]]) -> tuple[float, float, List[float]]:
        """Создает дефолтные entry_price, stop_loss и take_profits если они отсутствуют"""

        # Создаем entry_price если отсутствует
        if entry_price is None:
            # Сначала пытаемся получить актуальную цену с биржи
            market_price = await self._get_market_price(ticker, direction)

            if market_price:
                entry_price = market_price
                if self.logger:
                    await self.logger.info(f"💰 Получена актуальная цена {ticker} с биржи: {entry_price}")
            else:
                # Fallback на статичные цены если биржа недоступна
                default_prices = {
                    "BTC/USDT": 45000.0,
                    "ETH/USDT": 2500.0,
                    "BNB/USDT": 300.0,
                    "ADA/USDT": 0.5,
                    "SOL/USDT": 100.0,
                    "XRP/USDT": 0.6,
                    "DOGE/USDT": 0.08,
                    "MATIC/USDT": 0.8,
                    "DOT/USDT": 6.0,
                    "AVAX/USDT": 25.0
                }
                entry_price = default_prices.get(ticker, 1.0)  # Дефолт 1.0 для неизвестных пар
                if self.logger:
                    await self.logger.info(f"📊 Используем статичную цену {ticker}: {entry_price} (биржа недоступна)")

        # Создаем stop_loss если отсутствует
        if stop_loss is None:
            if direction == "Long":
                # Для лонга стоп ниже входа (имитируем 100% убыток, но ставим реалистичное значение)
                # Используем 90% от входной цены как максимальный убыток
                stop_loss = entry_price * 0.1  # 90% убыток
            else:  # Short
                # Для шорта стоп выше входа (имитируем 100% убыток)
                # Для шорта 100% убыток = цена удваивается
                stop_loss = entry_price * 2.0  # 100% убыток

        # Создаем take_profits если отсутствуют или их меньше 1 (20% профит)
        if not take_profits or not isinstance(take_profits, list) or len(take_profits) < 1:
            if direction == "Long":
                # Для лонга тейк выше входа на 20%
                take_profits = [entry_price * 1.2]  # 20% профит
            else:  # Short
                # Для шорта тейк ниже входа на 20%
                take_profits = [entry_price * 0.8]  # 20% профит

        return entry_price, stop_loss, take_profits

    def format_signal_json(self, signal: TradingSignal) -> str:
        """Форматирует торговый сигнал в JSON строку"""
        return signal.model_dump_json(indent=2, exclude_none=True)

    def format_signal_text(self, signal: TradingSignal) -> str:
        """Форматирует торговый сигнал в читаемый текст"""
        # Эмодзи для направления
        direction_emoji = "📈" if signal.direction == SignalDirection.LONG else "📉"

        text = f"🎯 **Торговый сигнал**\n\n"
        text += f"{direction_emoji} **{signal.direction}** {signal.ticker}\n\n"
        text += f"🎯 **Вход:** {signal.entry_price}\n"

        if signal.leverage:
            text += f"⚡ **Плечо:** {signal.leverage}x\n"

        if signal.take_profits:
            take_profits_str = " / ".join([str(tp) for tp in signal.take_profits])
            text += f"💰 **Тейки:** {take_profits_str}\n"

        if signal.stop_loss:
            text += f"🛡️ **Стоп:** {signal.stop_loss}\n"

        if signal.deposit_percentage:
            text += f"📊 **Депозит:** до {signal.deposit_percentage}%\n"

        return text 
