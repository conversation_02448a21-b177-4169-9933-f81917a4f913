#!/usr/bin/env python3
"""
Интеграция telegram-parser с Django API для работы с last_message_id.
Заменяет работу с JSON файлами на API вызовы.
"""

import asyncio
from typing import Optional
from api_client import TradingAPIClient, Channel


class APIIntegration:
    """Интеграция с API для работы с last_message_id."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = None
        self._channel_cache: dict[str, Channel] = {}
    
    async def _get_client(self):
        """Получает или создает клиент."""
        if self.client is None:
            self.client = TradingAPIClient(self.base_url)
            await self.client.__aenter__()
        return self.client
    
    async def close(self):
        """Закрывает соединение с API."""
        if self.client:
            await self.client.__aexit__(None, None, None)
            self.client = None
    
    async def load_last_message_id(self, channel_name: str) -> int:
        """
        Загружает ID последнего обработанного сообщения для канала из API.
        Заменяет функцию load_last_message_id из index_v3.py
        """
        try:
            # Проверяем кэш
            if channel_name in self._channel_cache:
                return self._channel_cache[channel_name].last_message_id
            
            # Получаем клиент и канал из API
            client = await self._get_client()
            channel = await client.get_channel_by_name(channel_name)
            if channel:
                self._channel_cache[channel_name] = channel
                return channel.last_message_id
            
            # Если канал не найден, возвращаем 0
            print(f"⚠️  Канал '{channel_name}' не найден в API, возвращаем 0")
            return 0
            
        except Exception as e:
            print(f"❌ Ошибка при загрузке last_message_id для {channel_name}: {e}")
            return 0
    
    async def save_last_message_id(self, channel_name: str, message_id: int) -> bool:
        """
        Сохраняет ID последнего обработанного сообщения для канала через API.
        Заменяет функцию save_last_message_id из index_v3.py
        """
        try:
            # Получаем клиент и канал из API
            client = await self._get_client()
            channel = await client.get_channel_by_name(channel_name)
            if not channel:
                print(f"❌ Канал '{channel_name}' не найден в API")
                return False
            
            # Обновляем last_message_id
            updated_channel = await client.update_channel_last_message_id(
                channel.id, message_id
            )
            
            if updated_channel:
                # Обновляем кэш
                self._channel_cache[channel_name] = updated_channel
                print(f"✅ {channel_name}: обновлен last_message_id до {message_id}")
                return True
            else:
                print(f"❌ Не удалось обновить last_message_id для {channel_name}")
                return False
                
        except Exception as e:
            print(f"❌ Ошибка при сохранении last_message_id для {channel_name}: {e}")
            return False
    
    async def update_all_last_message_ids(self) -> bool:
        """
        Обновляет все last_message_id на актуальные сообщения в каналах через API.
        Заменяет функцию update_all_last_message_ids из index_v3.py
        """
        try:
            print("🔄 Запущено обновление счетчиков последних сообщений через API...")
            
            # Получаем клиент и все каналы с last_message_id > 0
            client = await self._get_client()
            active_channels = await client.get_channels_with_last_message_id()
            print(f"📊 Найдено {len(active_channels)} активных каналов")
            
            updated_count = 0
            total_count = len(active_channels)
            
            for channel in active_channels:
                try:
                    print(f"🔄 Обновляем {channel.name}...")
                    
                    # Здесь можно добавить логику получения последнего сообщения из Telegram
                    # Пока просто выводим текущее значение
                    print(f"  Текущий last_message_id: {channel.last_message_id}")
                    updated_count += 1
                    
                    # Небольшая задержка между каналами
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    print(f"❌ Ошибка обновления {channel.name}: {e}")
                    continue
            
            print(f"🎯 Обновление завершено: {updated_count}/{total_count} каналов обработано")
            return True
            
        except Exception as e:
            print(f"❌ Критическая ошибка при обновлении счетчиков: {e}")
            return False
    
    async def get_channel_config_from_api(self, channel_name: str) -> Optional[dict]:
        """
        Получает конфигурацию канала из API.
        Может заменить функцию get_channel_config из index_v3.py
        """
        try:
            client = await self._get_client()
            channel = await client.get_channel_by_name(channel_name)
            if not channel:
                return None
            
            # Преобразуем в формат, совместимый с существующей логикой
            config = {
                "id": channel.telegram_id,  # Используем telegram_id как ID
                "name": channel.name,
                "leverage": channel.leverage or 10,
                "portfolio_percent": channel.portfolio_percent or 0.25,
                "wins": channel.wins or 0,
                "fails": channel.fails or 0,
                "wins_ratio": channel.wins_ratio or 0.0,
            }
            
            return config
            
        except Exception as e:
            print(f"❌ Ошибка при получении конфигурации канала {channel_name}: {e}")
            return None
    
    async def sync_channels_from_api(self) -> dict:
        """
        Синхронизирует каналы из API с локальной конфигурацией.
        Возвращает словарь каналов в формате SOURCE_CHANNELS.
        """
        try:
            print("🔄 Синхронизация каналов из API...")
            
            client = await self._get_client()
            channels = await client.get_channels(page_size=1000)
            source_channels = {}
            
            for channel in channels:
                # Создаем конфигурацию канала
                config = {
                    "id": channel.telegram_id,
                    "name": channel.name,
                    "leverage": channel.leverage or 10,
                    "portfolio_percent": channel.portfolio_percent or 0.25,
                    "wins": channel.wins or 0,
                    "fails": channel.fails or 0,
                    "wins_ratio": channel.wins_ratio or 0.0,
                }
                
                source_channels[channel.name] = config
                print(f"  ✅ {channel.name}: {channel.telegram_id}")
            
            print(f"📊 Синхронизировано {len(source_channels)} каналов")
            return source_channels
            
        except Exception as e:
            print(f"❌ Ошибка синхронизации каналов: {e}")
            return {}
    
    async def update_channel_statistics(self, channel_name: str, wins: int = None, fails: int = None) -> bool:
        """
        Обновляет статистику канала через API.
        Заменяет функцию update_channel_statistics из db_connection.py
        """
        try:
            client = await self._get_client()
            channel = await client.get_channel_by_name(channel_name)
            if not channel:
                print(f"❌ Канал '{channel_name}' не найден в API")
                return False
            
            # Подготавливаем данные для обновления
            update_data = {}
            if wins is not None:
                update_data["wins"] = wins
            if fails is not None:
                update_data["fails"] = fails
            
            if not update_data:
                print(f"⚠️  Нет данных для обновления статистики канала '{channel_name}'")
                return False
            
            # Обновляем канал через API
            updated_channel = await client.update_channel_stats(channel.id, update_data)
            
            if updated_channel:
                # Обновляем кэш
                self._channel_cache[channel_name] = updated_channel
                print(f"✅ Статистика канала '{channel_name}' обновлена: wins={updated_channel.wins}, fails={updated_channel.fails}")
                return True
            else:
                print(f"❌ Не удалось обновить статистику канала '{channel_name}'")
                return False
                
        except Exception as e:
            print(f"❌ Ошибка при обновлении статистики канала '{channel_name}': {e}")
            return False
    
    async def get_channel_statistics(self, channel_name: str) -> Optional[dict]:
        """
        Получает статистику канала через API.
        """
        try:
            client = await self._get_client()
            channel = await client.get_channel_by_name(channel_name)
            if not channel:
                return None
            
            return {
                "wins": channel.wins or 0,
                "fails": channel.fails or 0,
                "wins_ratio": channel.wins_ratio or 0.0,
            }
            
        except Exception as e:
            print(f"❌ Ошибка при получении статистики канала '{channel_name}': {e}")
            return None


async def test_integration():
    """Тестирует интеграцию с API."""
    integration = APIIntegration()
    
    try:
        print("🧪 Тестирование интеграции с API...")
        
        # Тест загрузки last_message_id
        print("\n1. Тест загрузки last_message_id:")
        last_id = await integration.load_last_message_id("Rose_Margin🔐CryptoVipTools")
        print(f"   Rose_Margin🔐CryptoVipTools: {last_id}")
        
        # Тест сохранения last_message_id
        print("\n2. Тест сохранения last_message_id:")
        success = await integration.save_last_message_id("Rose_Margin🔐CryptoVipTools", last_id + 50)
        print(f"   Результат: {'✅ Успешно' if success else '❌ Ошибка'}")
        
        # Тест получения конфигурации канала
        print("\n3. Тест получения конфигурации канала:")
        config = await integration.get_channel_config_from_api("Rose_Margin🔐CryptoVipTools")
        if config:
            print(f"   Конфигурация: {config}")
        
        # Тест синхронизации каналов
        print("\n4. Тест синхронизации каналов:")
        channels = await integration.sync_channels_from_api()
        print(f"   Синхронизировано каналов: {len(channels)}")
        
        # Тест обновления статистики
        print("\n5. Тест обновления статистики:")
        success = await integration.update_channel_statistics("Rose_Margin🔐CryptoVipTools", wins=5, fails=2)
        print(f"   Результат обновления статистики: {'✅ Успешно' if success else '❌ Ошибка'}")
        
        # Тест получения статистики
        print("\n6. Тест получения статистики:")
        stats = await integration.get_channel_statistics("Rose_Margin🔐CryptoVipTools")
        if stats:
            print(f"   Статистика: {stats}")
        
        print("\n🎉 Интеграция работает корректно!")
        
    except Exception as e:
        print(f"❌ Ошибка тестирования интеграции: {e}")
    finally:
        await integration.close()


if __name__ == "__main__":
    asyncio.run(test_integration()) 