# Быстрый старт - Telegram News Parser с AI анализом

## 1. Установка

```bash
python3 -m venv ./venv
source ./venv/bin/activate
pip install -r requirements.txt
```

## 2. Настройка

Создайте файл `.env` на основе `env.example`:

```bash
cp env.example .env
```

Заполните обязательные поля:
- `API_ID` и `API_HASH` - получите на https://my.telegram.org/auth
- `PHONE` - ваш номер телефона
- `ADMIN_ID` - ваш Telegram ID (узнайте у @userinfobot)

**Опциональные настройки:**
- `LLM_ANALYTICS_ENABLED` - включить/выключить AI анализ (по умолчанию `False`)
- `OPENROUTER_API_KEY` - API ключ с https://openrouter.ai/ (нужен только если AI анализ включен)
- `SHOW_SOURCE_HEADER` - показывать заголовок с источником канала (по умолчанию `False`)
- `FETCH_DIALOGS_ENABLED` - получать список диалогов при запуске (по умолчанию `False`)

## 3. Запуск

```bash
python index_v3.py
```

При первом запуске введите код подтверждения из Telegram.

## 4. Что вы получите

✅ **Автоматическая пересылка новостей** из:
- Newsmaker.club
- Binance Announcements  
- MarketTwits
- Coin Post

✅ **Настраиваемое отображение**:
- Заголовки с источником канала (включаются через `SHOW_SOURCE_HEADER=True`)
- По умолчанию новости пересылаются без заголовков

✅ **Дополнительные функции**:
- Получение списка всех диалогов/контактов (включается через `FETCH_DIALOGS_ENABLED=True`)
- Сохранение списка в файл для анализа доступных каналов

✅ **AI анализ каждой новости** (опционально):
- Тикеры финансовых инструментов
- Теги и категории
- Настроение от -1.0 до ****
- Включается через `LLM_ANALYTICS_ENABLED=True` в .env

✅ **Уведомления об ошибках** в Telegram

## 5. Troubleshooting

**Нет анализа новостей?**
- Проверьте, что `LLM_ANALYTICS_ENABLED=True` в .env
- Проверьте `OPENROUTER_API_KEY` в .env
- Убедитесь, что есть кредиты на openrouter.ai

**Не приходят уведомления?**
- Проверьте правильность `ADMIN_ID`
- Начните диалог с ботом (отправьте /start самому себе)

**Ошибки авторизации?**
- Удалите файл сессии `.session` и запустите заново
- Проверьте `API_ID` и `API_HASH` 