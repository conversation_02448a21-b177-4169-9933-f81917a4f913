#!/usr/bin/env python3
"""
Telegram бот для отображения баланса на бирже BingX
Отдельный скрипт, не зависит от основного проекта

Используемые переменные окружения:
- BALANCE_BOT_TOKEN: токен Telegram бота
- TARGET_CHAT_ID: ID чата для прослушивания команд
- BINGX_API_KEY: API ключ BingX
- BINGX_API_SECRET: API секрет BingX
- BINGX_TESTNET: использовать тестовую среду (True/False)
"""

import os
import asyncio
import logging
from typing import List, Dict, Optional
from datetime import datetime

# Telegram bot
from telegram import Update
from telegram.ext import Application, CommandHandler, ContextTypes
from telegram.constants import ParseMode

# BingX exchange
import ccxt

# Environment variables
from dotenv import load_dotenv

# Загружаем переменные окружения
load_dotenv()

# Настройка логирования
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class BalanceBot:
    """Telegram бот для отображения баланса на BingX"""
    
    def __init__(self):
        # Telegram bot token
        self.bot_token = os.getenv('BALANCE_BOT_TOKEN')
        if not self.bot_token:
            raise ValueError("BALANCE_BOT_TOKEN не найден в переменных окружения")
        
        # Target chat ID
        self.target_chat_id = os.getenv('TARGET_CHAT_ID')
        if not self.target_chat_id:
            raise ValueError("TARGET_CHAT_ID не найден в переменных окружения")
        
        try:
            self.target_chat_id = int(self.target_chat_id)
        except ValueError:
            raise ValueError("TARGET_CHAT_ID должен быть числом")
        
        # BingX API credentials
        self.bingx_api_key = os.getenv('BINGX_API_KEY')
        self.bingx_api_secret = os.getenv('BINGX_API_SECRET')
        self.bingx_testnet = os.getenv('BINGX_TESTNET', 'True').lower() == 'true'
        
        if not self.bingx_api_key or not self.bingx_api_secret:
            raise ValueError("BINGX_API_KEY и BINGX_API_SECRET должны быть установлены")
        
        # BingX exchange
        self.exchange = None
        self._init_exchange()
        
        # Telegram application
        self.application = None
    
    def _init_exchange(self):
        """Инициализация BingX exchange"""
        try:
            exchange_config = {
                'apiKey': self.bingx_api_key,
                'secret': self.bingx_api_secret,
                'sandbox': self.bingx_testnet,
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'future',  # Фьючерсы
                }
            }
            
            self.exchange = ccxt.bingx(exchange_config)
            
            # В тестовом режиме можем пропустить проверку подключения
            if not self.bingx_testnet:
                # Проверяем подключение только в продакшене
                markets = self.exchange.load_markets()
                logger.info(f"BingX подключен. Рынков: {len(markets)}")
            else:
                logger.info("BingX инициализирован в тестовом режиме")
                
        except Exception as e:
            logger.error(f"Ошибка инициализации BingX: {e}")
            raise
    
    async def get_balance_info(self) -> Dict:
        """Получает информацию о балансе и позициях"""
        try:
            # Получаем баланс
            balance = self.exchange.fetch_balance()
            usdt_info = balance.get('USDT', {})
            
            free_balance = usdt_info.get('free', 0.0)
            used_balance = usdt_info.get('used', 0.0)
            total_balance = usdt_info.get('total', free_balance + used_balance)
            
            # Получаем позиции
            positions = self.exchange.fetch_positions()
            
            # Фильтруем только открытые позиции
            open_positions = []
            if positions:
                for position in positions:
                    if not isinstance(position, dict):
                        continue
                    
                    contracts = position.get('contracts', 0) or 0
                    notional = position.get('notional', 0) or 0
                    size = position.get('size', 0) or 0
                    
                    if contracts != 0 or abs(notional) > 0.01 or abs(size) > 0:
                        # Вычисляем размер позиции в USD
                        position_size_usd = abs(notional)
                        if not position_size_usd:
                            mark_price = position.get('markPrice', 0.0) or position.get('mark_price', 0.0) or 0.0
                            if size and mark_price:
                                position_size_usd = abs(size * mark_price)
                            elif contracts and mark_price:
                                contract_size = position.get('contractSize', 1.0) or 1.0
                                position_size_usd = abs(contracts * contract_size * mark_price)
                        
                        open_positions.append({
                            'symbol': position.get('symbol', 'Unknown'),
                            'side': position.get('side', 'Unknown'),
                            'size': position.get('size', 0) or position.get('contracts', 0),
                            'notional_usd': position_size_usd,
                            'entry_price': position.get('entryPrice', 0) or position.get('entry_price', 0),
                            'mark_price': position.get('markPrice', 0) or position.get('mark_price', 0),
                            'unrealized_pnl': position.get('unrealizedPnl', 0) or position.get('unrealized_pnl', 0),
                            'percentage': position.get('percentage', 0) or 0
                        })
            
            return {
                'free_balance': free_balance,
                'used_balance': used_balance,
                'total_balance': total_balance,
                'positions': open_positions,
                'positions_count': len(open_positions),
                'testnet': self.bingx_testnet
            }
            
        except Exception as e:
            logger.error(f"Ошибка получения баланса: {e}")
            raise
    
    def format_balance_message(self, balance_info: Dict) -> str:
        """Форматирует сообщение с информацией о балансе"""
        testnet_emoji = "🧪" if balance_info['testnet'] else "💰"
        testnet_text = " (TESTNET)" if balance_info['testnet'] else ""
        
        message = f"{testnet_emoji} **БАЛАНС BINGX{testnet_text}**\n\n"
        
        # Информация о балансе
        message += f"Общий баланс: ${balance_info['total_balance']:.2f} USDT\n"
        message += f"Свободно: ${balance_info['free_balance']:.2f} USDT\n"
        message += f"В позициях: ${balance_info['used_balance']:.2f} USDT\n"
        
        # Portfolio Usage
        positions = balance_info['positions']
        total_portfolio_usd = sum(pos['notional_usd'] for pos in positions)
        portfolio_percent = (total_portfolio_usd / balance_info['total_balance'] * 100) if balance_info['total_balance'] > 0 else 0
        message += f"Portfolio Usage: ${total_portfolio_usd:.2f} ({portfolio_percent:.1f}%)\n\n"
        
        # Информация о позициях
        if positions:
            total_unrealized_pnl = 0.0
            for i, pos in enumerate(positions, 1):
                symbol = pos['symbol'].replace('/USDT:USDT', '')  # Убираем лишнее из символа
                side = pos['side'].upper()
                side_emoji = "🟢" if side == "LONG" else "🔴"
                
                size = pos['size']
                notional_usd = pos['notional_usd']
                entry_price = pos['entry_price']
                mark_price = pos['mark_price']
                unrealized_pnl = pos['unrealized_pnl']
                
                total_unrealized_pnl += unrealized_pnl
                
                message += f"{side_emoji} {i}. {symbol} {side}\n"
                message += f"   Размер: {size}\n"
                message += f"   Объем: ${notional_usd:.2f}\n"
                
                if entry_price > 0:
                    message += f"   Вход: ${entry_price:.6f}\n"
                if mark_price > 0:
                    message += f"   Текущая: ${mark_price:.6f}\n"
                
                if unrealized_pnl >= 0:
                    message += f"   💚 PnL: ${unrealized_pnl:.2f}\n"
                else:
                    message += f"   PnL: ${unrealized_pnl:.2f}\n"
                message += "\n"
            
            # Общий PnL
            if total_unrealized_pnl >= 0:
                message += f"💚 Общий PnL: ${total_unrealized_pnl:.2f}\n"
            else:
                message += f"Общий PnL: ${total_unrealized_pnl:.2f}\n"
            
        else:
            message += "Активные позиции: Нет открытых позиций\n"
        
        # Время обновления
        message += f"\n🕐 {datetime.now().strftime('%H:%M:%S')}"
        
        return message
    
    async def balance_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Обработчик команды /balance"""
        try:
            # Проверяем, что команда пришла из нужного чата
            if update.effective_chat.id != self.target_chat_id:
                logger.info(f"Команда /balance получена не из целевого чата: {update.effective_chat.id}")
                return
            
            # Отправляем уведомление о том, что обрабатываем запрос
            await update.message.reply_text("⏳ Получаю информацию о балансе...")
            
            # Получаем информацию о балансе
            balance_info = await self.get_balance_info()
            
            # Форматируем сообщение
            message = self.format_balance_message(balance_info)
            
            # Отправляем сообщение с балансом
            await update.message.reply_text(
                message,
                parse_mode=ParseMode.MARKDOWN
            )
            
            logger.info(f"Команда /balance успешно обработана для чата {update.effective_chat.id}")
            
        except Exception as e:
            logger.error(f"Ошибка обработки команды /balance: {e}")
            await update.message.reply_text(
                f"❌ Ошибка получения баланса: {str(e)}"
            )
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Обработчик команды /start"""
        if update.effective_chat.id != self.target_chat_id:
            return
        
        testnet_text = " (TESTNET)" if self.bingx_testnet else ""
        message = f"🤖 **Balance Bot** для BingX{testnet_text}\n\n"
        message += "Доступные команды:\n"
        message += "/balance - показать баланс и активные позиции\n"
        message += "/start - показать это сообщение"
        
        await update.message.reply_text(
            message,
            parse_mode=ParseMode.MARKDOWN
        )
    
    def run(self):
        """Запуск бота"""
        try:
            # Создаем приложение
            self.application = Application.builder().token(self.bot_token).build()
            
            # Добавляем обработчики команд
            self.application.add_handler(CommandHandler("balance", self.balance_command))
            self.application.add_handler(CommandHandler("start", self.start_command))
            
            logger.info(f"Balance Bot запущен. Слушаю чат ID: {self.target_chat_id}")
            logger.info(f"BingX режим: {'TESTNET' if self.bingx_testnet else 'PRODUCTION'}")
            
            # Запускаем бота
            self.application.run_polling(allowed_updates=["message"])
            
        except Exception as e:
            logger.error(f"Ошибка запуска бота: {e}")
            raise

def main():
    """Основная функция"""
    try:
        bot = BalanceBot()
        bot.run()
    except KeyboardInterrupt:
        logger.info("Получен сигнал завершения")
    except Exception as e:
        logger.error(f"Критическая ошибка: {e}")
        raise

if __name__ == "__main__":
    main()
