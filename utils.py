import re
from typing import Optional

def parse_duration(duration_str: str) -> int:
    """
    Парсит строку времени в секунды.
    
    Поддерживаемые форматы:
    - "1h30m10s" = 5410 секунд
    - "25m" = 1500 секунд  
    - "30s" = 30 секунд
    - "2h30m" = 9000 секунд
    - "0s" = 0 секунд (отключено)
    
    Args:
        duration_str: Строка времени
        
    Returns:
        int: Количество секунд
        
    Raises:
        ValueError: Если формат строки невалидный
    """
    if not duration_str or duration_str.strip() == "":
        return 0
    
    duration_str = duration_str.strip().lower()
    
    # Если строка содержит только цифры, считаем что это секунды
    if duration_str.isdigit():
        return int(duration_str)
    
    # Регулярное выражение для парсинга формата 1h30m10s
    pattern = r'(?:(\d+)h)?(?:(\d+)m)?(?:(\d+)s)?'
    match = re.match(pattern, duration_str)
    
    if not match:
        raise ValueError(f"Неверный формат времени: {duration_str}. Используйте формат как '1h30m10s', '25m', '30s'")
    
    hours, minutes, seconds = match.groups()
    
    total_seconds = 0
    if hours:
        total_seconds += int(hours) * 3600
    if minutes:
        total_seconds += int(minutes) * 60
    if seconds:
        total_seconds += int(seconds)
    
    # Проверяем что хотя бы одно значение было найдено
    if total_seconds == 0 and not any([hours, minutes, seconds]):
        raise ValueError(f"Неверный формат времени: {duration_str}. Используйте формат как '1h30m10s', '25m', '30s'")
    
    return total_seconds

def format_duration(seconds: int) -> str:
    """
    Форматирует секунды в читаемую строку.
    
    Args:
        seconds: Количество секунд
        
    Returns:
        str: Отформатированная строка времени
    """
    if seconds == 0:
        return "0s"
    
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    secs = seconds % 60
    
    parts = []
    if hours > 0:
        parts.append(f"{hours}h")
    if minutes > 0:
        parts.append(f"{minutes}m")
    if secs > 0:
        parts.append(f"{secs}s")
    
    return "".join(parts) if parts else "0s"

# Тесты для проверки
if __name__ == "__main__":
    test_cases = [
        ("1h30m10s", 5410),
        ("25m", 1500),
        ("30s", 30),
        ("2h30m", 9000),
        ("0s", 0),
        ("1h", 3600),
        ("2h", 7200),
        ("45m30s", 2730),
    ]
    
    for duration_str, expected in test_cases:
        result = parse_duration(duration_str)
        formatted = format_duration(result)
        print(f"{duration_str} -> {result}s (ожидалось {expected}s) -> {formatted}")
        assert result == expected, f"Ошибка: {duration_str} = {result}, ожидалось {expected}"
    
    print("✅ Все тесты прошли успешно!") 